#!/usr/bin/env python3
"""
Cache Clearing Script

This script clears the LLM cache to remove any stale 2024 date entries
that might be causing the date parsing regression.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def clear_llm_cache():
    """Clear the LLM cache to remove stale entries"""
    try:
        from app.utils.cache import llm_cache
        
        print("🧹 Clearing LLM cache...")
        
        # Get cache stats before clearing
        stats_before = llm_cache.get_stats()
        print(f"📊 Cache stats before clearing:")
        print(f"   Cache size: {stats_before['cache_size']} entries")
        print(f"   Hit rate: {stats_before['hit_rate_percent']}%")
        print(f"   Total hits: {stats_before['total_hits']}")
        print(f"   Total misses: {stats_before['total_misses']}")
        
        # Clear the cache
        llm_cache.clear()
        
        # Get cache stats after clearing
        stats_after = llm_cache.get_stats()
        print(f"\n✅ Cache cleared successfully!")
        print(f"📊 Cache stats after clearing:")
        print(f"   Cache size: {stats_after['cache_size']} entries")
        
        print(f"\n💡 This should resolve any issues with stale 2024 date entries.")
        print(f"💡 Next 'tomorrow' requests will generate fresh 2025 responses.")
        
    except Exception as e:
        print(f"❌ Error clearing cache: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function"""
    print("🔧 LLM CACHE CLEARING UTILITY")
    print("=" * 50)
    print("This script clears stale cache entries that might contain 2024 dates")
    print()
    
    clear_llm_cache()
    
    print("\n" + "=" * 50)
    print("🏁 Cache clearing complete!")

if __name__ == "__main__":
    main()
