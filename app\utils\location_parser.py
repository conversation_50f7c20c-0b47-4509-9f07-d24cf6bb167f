"""
Location parsing utilities for WhatsApp location sharing.

This module provides robust location parsing functionality for the WhatsApp booking agent,
handling both native WhatsApp location data and Google Maps URL parsing while maintaining
100% backward compatibility with existing text address functionality.

Supports:
1. Native WhatsApp location sharing (Latitude/Longitude parameters)
2. Google Maps URL parsing (multiple formats)
3. Coordinate validation and formatting
4. Error handling with Task 2.1 patterns
"""

import re
import logging
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlparse, parse_qs

from app.utils.exceptions import ValidationException, ErrorContext, log_exception

logger = logging.getLogger(__name__)


def extract_location_data(webhook_input) -> Optional[Dict[str, Any]]:
    """
    Extract location data from WhatsApp webhook input.
    
    Supports:
    1. Native WhatsApp location sharing (Latitude/Longitude parameters)
    2. Google Maps URL sharing (parsed from message body)
    
    Args:
        webhook_input: WebhookInput object with potential location data
        
    Returns:
        Dictionary with location data or None if no location found
        
    Example return format:
    {
        'type': 'native_whatsapp' | 'google_maps_url',
        'latitude': float,
        'longitude': float,
        'address': str,
        'label': str,
        'formatted_location': 'lat,lng',
        'display_text': 'formatted display string',
        'original_url': str (for URL type only)
    }
    """
    try:
        # Check for native WhatsApp location sharing
        if (hasattr(webhook_input, 'Latitude') and hasattr(webhook_input, 'Longitude') and
            webhook_input.Latitude is not None and webhook_input.Longitude is not None):
            
            # Validate coordinates
            if not _validate_coordinates(webhook_input.Latitude, webhook_input.Longitude):
                logger.warning(f"Invalid coordinates received: {webhook_input.Latitude}, {webhook_input.Longitude}")
                return None
            
            address = getattr(webhook_input, 'Address', '') or ''
            label = getattr(webhook_input, 'Label', '') or ''
            
            return {
                'type': 'native_whatsapp',
                'latitude': webhook_input.Latitude,
                'longitude': webhook_input.Longitude,
                'address': address,
                'label': label,
                'formatted_location': f"{webhook_input.Latitude},{webhook_input.Longitude}",
                'display_text': _format_location_display(
                    webhook_input.Latitude, 
                    webhook_input.Longitude,
                    address,
                    label
                )
            }
        
        # Check for Google Maps URL in message body
        if hasattr(webhook_input, 'Body') and webhook_input.Body:
            maps_data = parse_google_maps_url(webhook_input.Body)
            if maps_data:
                return {
                    'type': 'google_maps_url',
                    'latitude': maps_data['latitude'],
                    'longitude': maps_data['longitude'],
                    'address': maps_data.get('address', ''),
                    'label': maps_data.get('label', ''),
                    'formatted_location': f"{maps_data['latitude']},{maps_data['longitude']}",
                    'display_text': _format_location_display(
                        maps_data['latitude'],
                        maps_data['longitude'],
                        maps_data.get('address'),
                        maps_data.get('label')
                    ),
                    'original_url': maps_data.get('original_url', '')
                }
        
        return None
        
    except Exception as e:
        # Use Task 2.1 error handling patterns
        context = ErrorContext(
            operation="extract_location_data",
            additional_data={
                "has_coordinates": hasattr(webhook_input, 'Latitude') and webhook_input.Latitude is not None,
                "has_body": hasattr(webhook_input, 'Body') and bool(webhook_input.Body)
            }
        )
        log_exception(e, context=context)
        return None


def parse_google_maps_url(text: str) -> Optional[Dict[str, Any]]:
    """
    Parse Google Maps URLs to extract coordinates.
    
    Supported formats:
    - https://maps.google.com/?q=40.7128,-74.0060
    - https://maps.google.com/maps?q=40.7128,-74.0060
    - https://goo.gl/maps/xyz123 (shortened URLs - basic support)
    - https://maps.app.goo.gl/xyz123 (new shortened format)
    
    Args:
        text: Message text potentially containing Google Maps URL
        
    Returns:
        Dictionary with latitude, longitude and other data or None if no valid coordinates found
    """
    try:
        if not text or not isinstance(text, str):
            return None
            
        # Find Google Maps URLs in text
        url_patterns = [
            r'https?://maps\.google\.com/[^\s]+',
            r'https?://goo\.gl/maps/[^\s]+',
            r'https?://maps\.app\.goo\.gl/[^\s]+'
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for url in matches:
                coords = _extract_coordinates_from_url(url)
                if coords:
                    return {
                        'latitude': coords[0],
                        'longitude': coords[1],
                        'original_url': url
                    }
        
        return None
        
    except Exception as e:
        # Use Task 2.1 error handling patterns
        context = ErrorContext(
            operation="parse_google_maps_url",
            additional_data={"text_length": len(text) if text else 0}
        )
        log_exception(e, context=context)
        return None


def format_location_for_odoo(location_data: Dict[str, Any]) -> str:
    """
    Format location data for Odoo API (latitude,longitude format).
    
    Args:
        location_data: Location data dictionary from extract_location_data()
        
    Returns:
        GPS coordinates string in "latitude,longitude" format for Odoo API
        Returns empty string if location_data is invalid or missing coordinates
    """
    try:
        if not location_data or not isinstance(location_data, dict):
            return ""
        
        lat = location_data.get('latitude')
        lng = location_data.get('longitude')
        
        if lat is not None and lng is not None:
            # Validate coordinates before formatting
            if _validate_coordinates(lat, lng):
                return f"{lat},{lng}"
            else:
                logger.warning(f"Invalid coordinates for Odoo formatting: {lat}, {lng}")
                return ""
        
        return ""
        
    except Exception as e:
        # Use Task 2.1 error handling patterns
        context = ErrorContext(
            operation="format_location_for_odoo",
            additional_data={"location_data_type": type(location_data).__name__}
        )
        log_exception(e, context=context)
        return ""


def _validate_coordinates(lat: float, lng: float) -> bool:
    """
    Validate latitude and longitude ranges.
    
    Args:
        lat: Latitude value
        lng: Longitude value
        
    Returns:
        True if coordinates are within valid ranges, False otherwise
    """
    try:
        # Check if values are numeric
        if not isinstance(lat, (int, float)) or not isinstance(lng, (int, float)):
            return False
            
        # Check coordinate ranges
        # Latitude: -90 to 90 degrees
        # Longitude: -180 to 180 degrees
        return -90 <= lat <= 90 and -180 <= lng <= 180
        
    except Exception:
        return False


def _extract_coordinates_from_url(url: str) -> Optional[Tuple[float, float]]:
    """
    Extract latitude and longitude from Google Maps URL.
    
    Args:
        url: Google Maps URL
        
    Returns:
        Tuple of (latitude, longitude) or None if extraction fails
    """
    try:
        if not url or not isinstance(url, str):
            return None
            
        parsed = urlparse(url)
        
        # Check query parameters for coordinates
        query_params = parse_qs(parsed.query)
        
        # Look for 'q' parameter with coordinates
        if 'q' in query_params:
            q_value = query_params['q'][0]
            coords = _parse_coordinate_string(q_value)
            if coords:
                return coords
        
        # Look for direct coordinate patterns in URL
        coord_pattern = r'(-?\d+\.?\d*),(-?\d+\.?\d*)'
        match = re.search(coord_pattern, url)
        if match:
            try:
                lat, lng = float(match.group(1)), float(match.group(2))
                if _validate_coordinates(lat, lng):
                    return (lat, lng)
            except ValueError:
                pass
        
        return None
        
    except Exception as e:
        logger.debug(f"Error extracting coordinates from URL {url}: {e}")
        return None


def _parse_coordinate_string(coord_str: str) -> Optional[Tuple[float, float]]:
    """
    Parse coordinate string like '40.7128,-74.0060'.
    
    Args:
        coord_str: String containing coordinates
        
    Returns:
        Tuple of (latitude, longitude) or None if parsing fails
    """
    try:
        if not coord_str or not isinstance(coord_str, str):
            return None
            
        # Remove any extra characters and split by comma
        cleaned = re.sub(r'[^\d.,-]', '', coord_str)
        parts = cleaned.split(',')
        
        if len(parts) == 2:
            try:
                lat, lng = float(parts[0]), float(parts[1])
                if _validate_coordinates(lat, lng):
                    return (lat, lng)
            except ValueError:
                pass
        
        return None
        
    except Exception:
        return None


def _format_location_display(lat: float, lng: float, address: str = None, label: str = None) -> str:
    """
    Format location data for user display.
    
    Args:
        lat: Latitude
        lng: Longitude
        address: Optional address string
        label: Optional label/name
        
    Returns:
        Formatted display string for user-friendly location representation
    """
    try:
        parts = []
        
        # Add label if available
        if label and label.strip():
            parts.append(label.strip())
            
        # Add address if available
        if address and address.strip():
            parts.append(address.strip())
        
        # Always include coordinates as fallback
        coord_str = f"📍 {lat:.6f}, {lng:.6f}"
        parts.append(coord_str)
        
        # Join parts with separator
        return " - ".join(parts) if len(parts) > 1 else parts[0]
        
    except Exception as e:
        # Fallback to basic coordinate display
        logger.warning(f"Error formatting location display: {e}")
        try:
            return f"📍 {lat:.6f}, {lng:.6f}"
        except Exception:
            return "📍 Location"
