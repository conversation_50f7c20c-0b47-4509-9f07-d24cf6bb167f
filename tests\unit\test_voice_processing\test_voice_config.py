# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Unit tests for voice processing configuration.

Tests voice configuration management, environment variable validation,
feature flags, and configuration documentation.
"""

import os
from unittest.mock import patch
from typing import Generator

import pytest

from app.utils.voice_config import (
    VoiceProcessingConfig, get_voice_config, get_environment_variables_documentation,
    DEFAULT_LANGUAGE_CODE, SUPPORTED_LANGUAGE_CODES, DEFAULT_VOICE_PROCESSING_ENABLED,
    DEFAULT_TRANSCRIPTION_CACHE_TTL_MINUTES
)
from app.utils.exceptions import ConfigurationException


@pytest.fixture
def clean_env() -> Generator[None, None, None]:
    """Clean environment variables for testing."""
    env_vars = [
        'VOICE_PROCESSING_ENABLED',
        'GOOGLE_CLOUD_PROJECT_ID',
        'SPEECH_TO_TEXT_LANGUAGE_CODE',
        'MAX_AUDIO_FILE_SIZE_MB',
        'TRANSCRIPTION_CACHE_TTL_MINUTES'
    ]
    
    # Store original values
    original_values = {}
    for var in env_vars:
        original_values[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]
    
    yield
    
    # Restore original values
    for var, value in original_values.items():
        if value is not None:
            os.environ[var] = value
        elif var in os.environ:
            del os.environ[var]


class TestVoiceProcessingConfig:
    """Test cases for VoiceProcessingConfig class."""

    def test_config_creation_with_defaults(self):
        """Test VoiceProcessingConfig creation with default values."""
        config = VoiceProcessingConfig()
        
        assert config.google_cloud_project_id is None
        assert config.speech_to_text_language_code == DEFAULT_LANGUAGE_CODE
        assert config.voice_processing_enabled == DEFAULT_VOICE_PROCESSING_ENABLED
        assert config.transcription_cache_ttl_minutes == DEFAULT_TRANSCRIPTION_CACHE_TTL_MINUTES

    def test_config_creation_with_custom_values(self):
        """Test VoiceProcessingConfig creation with custom values."""
        config = VoiceProcessingConfig(
            google_cloud_project_id='test-project',
            speech_to_text_language_code='ar-SA',
            voice_processing_enabled=True,
            max_audio_file_size_mb=20,
            transcription_cache_ttl_minutes=120
        )
        
        assert config.google_cloud_project_id == 'test-project'
        assert config.speech_to_text_language_code == 'ar-SA'
        assert config.voice_processing_enabled is True
        assert config.max_audio_file_size_mb == 20
        assert config.transcription_cache_ttl_minutes == 120

    def test_config_validation_success(self):
        """Test successful configuration validation."""
        config = VoiceProcessingConfig(
            google_cloud_project_id='test-project',
            voice_processing_enabled=True,
            transcription_cache_ttl_minutes=60
        )
        
        # Should not raise exception
        config.validate()

    def test_config_validation_missing_project_id(self):
        """Test validation failure when project ID is missing."""
        config = VoiceProcessingConfig(
            voice_processing_enabled=True,
            google_cloud_project_id=None
        )
        
        with pytest.raises(ConfigurationException) as exc_info:
            config.validate()
        
        assert 'GOOGLE_CLOUD_PROJECT_ID is required' in str(exc_info.value)

    def test_config_validation_invalid_language_code(self):
        """Test validation failure for invalid language code."""
        config = VoiceProcessingConfig(
            speech_to_text_language_code='invalid-lang'
        )
        
        with pytest.raises(ConfigurationException) as exc_info:
            config.validate()
        
        assert 'Invalid language code' in str(exc_info.value)

    def test_config_validation_invalid_cache_ttl(self):
        """Test validation failure for invalid cache TTL."""
        config = VoiceProcessingConfig(
            transcription_cache_ttl_minutes=0
        )
        
        with pytest.raises(ConfigurationException) as exc_info:
            config.validate()
        
        assert 'Invalid TRANSCRIPTION_CACHE_TTL_MINUTES' in str(exc_info.value)

    def test_config_validation_disabled_voice_processing(self):
        """Test validation when voice processing is disabled."""
        config = VoiceProcessingConfig(
            voice_processing_enabled=False,
            google_cloud_project_id=None
        )
        
        # Should not raise exception when disabled
        config.validate()

    def test_is_voice_processing_available_enabled(self):
        """Test voice processing availability when enabled and configured."""
        config = VoiceProcessingConfig(
            voice_processing_enabled=True,
            google_cloud_project_id='test-project'
        )
        
        assert config.is_voice_processing_available() is True

    def test_is_voice_processing_available_disabled(self):
        """Test voice processing availability when disabled."""
        config = VoiceProcessingConfig(
            voice_processing_enabled=False
        )
        
        assert config.is_voice_processing_available() is False

    def test_is_voice_processing_available_missing_config(self):
        """Test voice processing availability when configuration is missing."""
        config = VoiceProcessingConfig(
            voice_processing_enabled=True,
            google_cloud_project_id=None
        )
        
        assert config.is_voice_processing_available() is False

    def test_to_dict(self):
        """Test configuration serialization to dictionary."""
        config = VoiceProcessingConfig(
            google_cloud_project_id='test-project',
            speech_to_text_language_code='ar-SA',
            voice_processing_enabled=True,
            max_audio_file_size_mb=20,
            transcription_cache_ttl_minutes=120
        )
        
        result = config.to_dict()
        
        assert result['google_cloud_project_id'] == 'configured'
        assert result['speech_to_text_language_code'] == 'ar-SA'
        assert result['voice_processing_enabled'] is True
        assert result['max_audio_file_size_mb'] == 20
        assert result['transcription_cache_ttl_minutes'] == 120
        assert result['is_available'] is True

    def test_to_dict_no_project_id(self):
        """Test configuration serialization without project ID."""
        config = VoiceProcessingConfig()
        
        result = config.to_dict()
        
        assert result['google_cloud_project_id'] is None
        assert result['is_available'] is False


class TestVoiceConfigFromEnvironment:
    """Test cases for loading configuration from environment variables."""

    def test_from_env_with_defaults(self, clean_env):
        """Test loading configuration with default values."""
        config = VoiceProcessingConfig.from_env()
        
        assert config.voice_processing_enabled is False
        assert config.speech_to_text_language_code == DEFAULT_LANGUAGE_CODE
        assert config.transcription_cache_ttl_minutes == DEFAULT_TRANSCRIPTION_CACHE_TTL_MINUTES

    def test_from_env_with_custom_values(self, clean_env):
        """Test loading configuration with custom environment values."""
        os.environ['VOICE_PROCESSING_ENABLED'] = 'true'
        os.environ['GOOGLE_CLOUD_PROJECT_ID'] = 'test-project'
        os.environ['SPEECH_TO_TEXT_LANGUAGE_CODE'] = 'ar-SA'
        os.environ['MAX_AUDIO_FILE_SIZE_MB'] = '20'
        os.environ['TRANSCRIPTION_CACHE_TTL_MINUTES'] = '120'
        
        config = VoiceProcessingConfig.from_env()
        
        assert config.voice_processing_enabled is True
        assert config.google_cloud_project_id == 'test-project'
        assert config.speech_to_text_language_code == 'ar-SA'
        assert config.max_audio_file_size_mb == 20
        assert config.transcription_cache_ttl_minutes == 120

    def test_from_env_invalid_boolean(self, clean_env):
        """Test loading configuration with invalid boolean value."""
        os.environ['VOICE_PROCESSING_ENABLED'] = 'invalid'
        
        with pytest.raises(ConfigurationException) as exc_info:
            VoiceProcessingConfig.from_env()
        
        assert 'Invalid VOICE_PROCESSING_ENABLED value' in str(exc_info.value)

    def test_from_env_invalid_integer(self, clean_env):
        """Test loading configuration with invalid integer value."""
        os.environ['MAX_AUDIO_FILE_SIZE_MB'] = 'invalid'
        
        with pytest.raises(ConfigurationException) as exc_info:
            VoiceProcessingConfig.from_env()
        
        assert 'Invalid MAX_AUDIO_FILE_SIZE_MB value' in str(exc_info.value)

    def test_from_env_invalid_cache_ttl(self, clean_env):
        """Test loading configuration with invalid cache TTL value."""
        os.environ['TRANSCRIPTION_CACHE_TTL_MINUTES'] = 'invalid'
        
        with pytest.raises(ConfigurationException) as exc_info:
            VoiceProcessingConfig.from_env()
        
        assert 'Invalid TRANSCRIPTION_CACHE_TTL_MINUTES value' in str(exc_info.value)

    def test_from_env_validation_failure(self, clean_env):
        """Test loading configuration that fails validation."""
        os.environ['VOICE_PROCESSING_ENABLED'] = 'true'
        # Missing GOOGLE_CLOUD_PROJECT_ID
        
        with pytest.raises(ConfigurationException) as exc_info:
            VoiceProcessingConfig.from_env()
        
        assert 'GOOGLE_CLOUD_PROJECT_ID is required' in str(exc_info.value)


class TestGetVoiceConfig:
    """Test cases for get_voice_config function."""

    @patch('app.utils.voice_config.VoiceProcessingConfig.from_env')
    def test_get_voice_config_success(self, mock_from_env):
        """Test successful voice configuration retrieval."""
        mock_config = VoiceProcessingConfig()
        mock_from_env.return_value = mock_config
        
        config = get_voice_config()
        
        assert config == mock_config
        mock_from_env.assert_called_once()

    @patch('app.utils.voice_config.VoiceProcessingConfig.from_env')
    def test_get_voice_config_caching(self, mock_from_env):
        """Test voice configuration caching."""
        mock_config = VoiceProcessingConfig()
        mock_from_env.return_value = mock_config
        
        # Call twice
        config1 = get_voice_config()
        config2 = get_voice_config()
        
        assert config1 == config2
        # Should only call from_env once due to caching
        mock_from_env.assert_called_once()


class TestEnvironmentVariablesDocumentation:
    """Test cases for environment variables documentation."""

    def test_get_environment_variables_documentation(self):
        """Test environment variables documentation retrieval."""
        docs = get_environment_variables_documentation()
        
        assert isinstance(docs, dict)
        
        # Check required environment variables are documented
        required_vars = [
            'VOICE_PROCESSING_ENABLED',
            'GOOGLE_CLOUD_PROJECT_ID',
            'SPEECH_TO_TEXT_LANGUAGE_CODE',
            'MAX_AUDIO_FILE_SIZE_MB',
            'TRANSCRIPTION_CACHE_TTL_MINUTES'
        ]
        
        for var in required_vars:
            assert var in docs
            assert 'description' in docs[var]
            assert 'required' in docs[var]
            assert 'example' in docs[var]

    def test_documentation_structure(self):
        """Test documentation structure for each variable."""
        docs = get_environment_variables_documentation()
        
        for var_name, var_doc in docs.items():
            assert isinstance(var_doc, dict)
            assert 'description' in var_doc
            assert 'required' in var_doc
            assert 'example' in var_doc
            
            # Check that required field is valid
            assert var_doc['required'] in ['Required', 'Optional']


class TestVoiceConfigConstants:
    """Test cases for voice configuration constants."""

    def test_default_language_code(self):
        """Test default language code constant."""
        assert DEFAULT_LANGUAGE_CODE == "en-US"

    def test_supported_language_codes(self):
        """Test supported language codes constant."""
        assert isinstance(SUPPORTED_LANGUAGE_CODES, list)
        assert "en-US" in SUPPORTED_LANGUAGE_CODES
        assert "ar-SA" in SUPPORTED_LANGUAGE_CODES

    def test_default_voice_processing_enabled(self):
        """Test default voice processing enabled constant."""
        assert DEFAULT_VOICE_PROCESSING_ENABLED is False

    def test_default_transcription_cache_ttl(self):
        """Test default transcription cache TTL constant."""
        assert DEFAULT_TRANSCRIPTION_CACHE_TTL_MINUTES == 60
