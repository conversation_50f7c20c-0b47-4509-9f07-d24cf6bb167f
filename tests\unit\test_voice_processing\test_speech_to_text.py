# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Unit tests for Speech-to-Text service.

Tests Google Cloud Speech-to-Text API integration, transcription caching,
retry logic, and error handling for voice message processing.
"""

import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from typing import Generator

import pytest

from app.utils.speech_to_text import (
    SpeechToTextService, TranscriptionResult,
    transcribe_audio_file, get_speech_to_text_service
)


@pytest.fixture
def mock_speech_client():
    """Create mock Google Cloud Speech client."""
    mock_client = Mock()
    mock_response = Mock()
    mock_response.results = [
        Mock(alternatives=[
            Mock(transcript="Hello, I would like to book an appointment", confidence=0.95)
        ])
    ]
    mock_client.recognize.return_value = mock_response
    return mock_client


@pytest.fixture
def speech_service(mock_speech_client):
    """Create SpeechToTextService instance with mocked client."""
    with patch('app.utils.speech_to_text.speech.SpeechClient', return_value=mock_speech_client):
        service = SpeechToTextService()
        service.client = mock_speech_client
        return service


@pytest.fixture
def temp_audio_file() -> Generator[str, None, None]:
    """Create temporary audio file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
        temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'x' * 1000)
        temp_file_path = temp_file.name
    
    yield temp_file_path
    
    if os.path.exists(temp_file_path):
        os.unlink(temp_file_path)


class TestSpeechToTextService:
    """Test cases for SpeechToTextService class."""

    def test_init_with_valid_config(self):
        """Test SpeechToTextService initialization with valid configuration."""
        with patch('app.utils.speech_to_text.get_voice_config') as mock_config:
            mock_config.return_value.google_cloud_project_id = 'test-project'
            
            with patch('app.utils.speech_to_text.speech.SpeechClient') as mock_client_class:
                service = SpeechToTextService()
                assert service.project_id == 'test-project'
                mock_client_class.assert_called_once()

    def test_init_without_project_id(self):
        """Test SpeechToTextService initialization without project ID."""
        with patch('app.utils.speech_to_text.get_voice_config') as mock_config:
            mock_config.return_value.google_cloud_project_id = None
            
            with pytest.raises(Exception) as exc_info:
                SpeechToTextService()
            
            assert 'Google Cloud project ID not configured' in str(exc_info.value)

    @patch('app.utils.cache.get_cached_transcription')
    @patch('app.utils.cache.cache_transcription_result')
    def test_transcribe_audio_success(self, mock_cache_set, mock_cache_get, speech_service, temp_audio_file):
        """Test successful audio transcription."""
        # Mock cache miss
        mock_cache_get.return_value = None
        
        result = speech_service.transcribe_audio(temp_audio_file, 'audio/ogg')
        
        assert result.success
        assert result.transcript == "Hello, I would like to book an appointment"
        assert result.confidence == 0.95
        assert result.language_code == "en-US"
        assert result.processing_time > 0
        
        # Verify caching was called
        mock_cache_set.assert_called_once()

    @patch('app.utils.cache.get_cached_transcription')
    def test_transcribe_audio_cache_hit(self, mock_cache_get, speech_service, temp_audio_file):
        """Test transcription with cache hit."""
        # Mock cache hit
        cached_result = {
            'success': True,
            'transcript': 'Cached transcription result',
            'confidence': 0.88,
            'language_code': 'en-US',
            'error_message': None,
            'metadata': None
        }
        mock_cache_get.return_value = cached_result
        
        result = speech_service.transcribe_audio(temp_audio_file, 'audio/ogg')
        
        assert result.success
        assert result.transcript == 'Cached transcription result'
        assert result.confidence == 0.88
        
        # Verify API was not called
        speech_service.client.recognize.assert_not_called()

    def test_transcribe_audio_api_error(self, speech_service, temp_audio_file):
        """Test transcription with API error."""
        # Mock API error
        speech_service.client.recognize.side_effect = Exception("API Error")
        
        with patch('app.utils.cache.get_cached_transcription', return_value=None):
            result = speech_service.transcribe_audio(temp_audio_file, 'audio/ogg')
        
        assert not result.success
        assert 'API Error' in result.error_message
        assert result.transcript == ''
        assert result.confidence == 0.0

    def test_transcribe_audio_empty_response(self, speech_service, temp_audio_file):
        """Test transcription with empty API response."""
        # Mock empty response
        mock_response = Mock()
        mock_response.results = []
        speech_service.client.recognize.return_value = mock_response
        
        with patch('app.utils.cache.get_cached_transcription', return_value=None):
            result = speech_service.transcribe_audio(temp_audio_file, 'audio/ogg')
        
        assert not result.success
        assert 'No transcription results' in result.error_message

    def test_transcribe_audio_retry_logic(self, speech_service, temp_audio_file):
        """Test transcription retry logic on failures."""
        # Mock first two calls to fail, third to succeed
        speech_service.client.recognize.side_effect = [
            Exception("Temporary error"),
            Exception("Another error"),
            Mock(results=[Mock(alternatives=[Mock(transcript="Success", confidence=0.9)])])
        ]
        
        with patch('app.utils.cache.get_cached_transcription', return_value=None):
            with patch('time.sleep'):  # Speed up test
                result = speech_service.transcribe_audio(temp_audio_file, 'audio/ogg')
        
        assert result.success
        assert result.transcript == "Success"
        assert speech_service.client.recognize.call_count == 3

    def test_transcribe_audio_max_retries_exceeded(self, speech_service, temp_audio_file):
        """Test transcription when max retries are exceeded."""
        # Mock all calls to fail
        speech_service.client.recognize.side_effect = Exception("Persistent error")
        
        with patch('app.utils.cache.get_cached_transcription', return_value=None):
            with patch('time.sleep'):  # Speed up test
                result = speech_service.transcribe_audio(temp_audio_file, 'audio/ogg')
        
        assert not result.success
        assert 'Persistent error' in result.error_message
        assert speech_service.client.recognize.call_count == 3  # Max retries

    def test_transcribe_audio_with_language_hint(self, speech_service, temp_audio_file):
        """Test transcription with language hint."""
        with patch('app.utils.cache.get_cached_transcription', return_value=None):
            result = speech_service.transcribe_audio(temp_audio_file, 'audio/ogg', language_hint='ar-SA')
        
        assert result.success
        
        # Verify language hint was used in API call
        call_args = speech_service.client.recognize.call_args
        config = call_args[0][0].config
        assert config.language_code == 'ar-SA'

    def test_transcribe_audio_file_not_found(self, speech_service):
        """Test transcription with non-existent file."""
        result = speech_service.transcribe_audio('/nonexistent/file.ogg', 'audio/ogg')
        
        assert not result.success
        assert 'File not found' in result.error_message

    def test_get_supported_formats(self, speech_service):
        """Test getting supported audio formats."""
        formats = speech_service.get_supported_formats()
        
        assert isinstance(formats, list)
        assert 'audio/ogg' in formats
        assert 'audio/mpeg' in formats
        assert 'audio/mp4' in formats

    def test_is_format_supported(self, speech_service):
        """Test format support checking."""
        assert speech_service.is_format_supported('audio/ogg')
        assert speech_service.is_format_supported('audio/mpeg')
        assert not speech_service.is_format_supported('audio/wav')
        assert not speech_service.is_format_supported('video/mp4')


class TestTranscriptionResult:
    """Test cases for TranscriptionResult dataclass."""

    def test_transcription_result_success(self):
        """Test TranscriptionResult for successful transcription."""
        result = TranscriptionResult(
            success=True,
            transcript="Hello world",
            confidence=0.95,
            language_code="en-US",
            processing_time=2.5
        )
        
        assert result.success
        assert result.transcript == "Hello world"
        assert result.confidence == 0.95
        assert result.language_code == "en-US"
        assert result.processing_time == 2.5
        assert result.error_message is None

    def test_transcription_result_failure(self):
        """Test TranscriptionResult for failed transcription."""
        result = TranscriptionResult(
            success=False,
            error_message="Transcription failed"
        )
        
        assert not result.success
        assert result.error_message == "Transcription failed"
        assert result.transcript == ""
        assert result.confidence == 0.0
        assert result.language_code == ""

    def test_transcription_result_defaults(self):
        """Test TranscriptionResult with default values."""
        result = TranscriptionResult(success=True)
        
        assert result.success
        assert result.transcript == ""
        assert result.confidence == 0.0
        assert result.language_code == ""
        assert result.error_message is None
        assert result.metadata is None
        assert result.processing_time == 0.0


class TestConvenienceFunctions:
    """Test cases for convenience functions."""

    @patch('app.utils.speech_to_text.SpeechToTextService')
    def test_transcribe_audio_file(self, mock_service_class):
        """Test transcribe_audio_file convenience function."""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        mock_result = TranscriptionResult(success=True, transcript="Test")
        mock_service.transcribe_audio.return_value = mock_result
        
        result = transcribe_audio_file('/test/file.ogg', 'audio/ogg')
        
        assert result.success
        assert result.transcript == "Test"
        mock_service.transcribe_audio.assert_called_once_with('/test/file.ogg', 'audio/ogg', None)

    @patch('app.utils.speech_to_text.SpeechToTextService')
    def test_get_speech_to_text_service(self, mock_service_class):
        """Test get_speech_to_text_service convenience function."""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        service = get_speech_to_text_service()
        
        assert service == mock_service
        mock_service_class.assert_called_once()


class TestSpeechToTextIntegration:
    """Integration tests for Speech-to-Text service with real-like scenarios."""

    @patch('app.utils.speech_to_text.speech.SpeechClient')
    def test_end_to_end_transcription_flow(self, mock_client_class, temp_audio_file):
        """Test complete transcription flow with realistic scenario."""
        # Setup mock client
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Mock successful API response
        mock_response = Mock()
        mock_response.results = [
            Mock(alternatives=[
                Mock(transcript="I need to book a massage appointment for tomorrow at 3 PM", confidence=0.92)
            ])
        ]
        mock_client.recognize.return_value = mock_response
        
        # Mock voice config
        with patch('app.utils.speech_to_text.get_voice_config') as mock_config:
            mock_config.return_value.google_cloud_project_id = 'test-project'
            
            # Mock cache miss
            with patch('app.utils.cache.get_cached_transcription', return_value=None):
                with patch('app.utils.cache.cache_transcription_result') as mock_cache_set:
                    
                    service = SpeechToTextService()
                    result = service.transcribe_audio(temp_audio_file, 'audio/ogg', 'en-US')
                    
                    # Verify successful transcription
                    assert result.success
                    assert "massage appointment" in result.transcript
                    assert result.confidence > 0.9
                    assert result.language_code == "en-US"
                    
                    # Verify caching was called
                    mock_cache_set.assert_called_once()
                    
                    # Verify API was called with correct parameters
                    mock_client.recognize.assert_called_once()
                    call_args = mock_client.recognize.call_args[0]
                    assert call_args[0].config.language_code == 'en-US'
