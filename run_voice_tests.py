#!/usr/bin/env python3
"""
Voice Processing Test Runner

Comprehensive test runner for voice message processing system.
Runs unit tests, integration tests, performance tests, and generates reports.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any


def run_command(command: List[str], description: str) -> Dict[str, Any]:
    """
    Run a command and return results.
    
    Args:
        command: Command to run as list of strings
        description: Description of the command for logging
        
    Returns:
        Dictionary with command results
    """
    print(f"\n🔍 {description}")
    print(f"Command: {' '.join(command)}")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        success = result.returncode == 0
        print(f"{'✅ PASSED' if success else '❌ FAILED'} ({duration:.1f}s)")
        
        return {
            'success': success,
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'duration': duration,
            'description': description
        }
        
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT - Command exceeded 5 minutes")
        return {
            'success': False,
            'returncode': -1,
            'stdout': '',
            'stderr': 'Command timeout',
            'duration': 300,
            'description': description
        }
    except Exception as e:
        print(f"❌ ERROR - {e}")
        return {
            'success': False,
            'returncode': -1,
            'stdout': '',
            'stderr': str(e),
            'duration': 0,
            'description': description
        }


def run_unit_tests(verbose: bool = False) -> Dict[str, Any]:
    """Run unit tests for voice processing components."""
    command = [
        "python", "-m", "pytest",
        "tests/unit/test_voice_processing/",
        "-v" if verbose else "-q",
        "--tb=short",
        "--durations=10",
        "-m", "not slow"
    ]
    
    return run_command(command, "Running Voice Processing Unit Tests")


def run_integration_tests(verbose: bool = False) -> Dict[str, Any]:
    """Run integration tests for voice processing."""
    command = [
        "python", "-m", "pytest",
        "tests/integration/test_voice_processing_integration.py",
        "-v" if verbose else "-q",
        "--tb=short",
        "--durations=10"
    ]
    
    return run_command(command, "Running Voice Processing Integration Tests")


def run_performance_tests(verbose: bool = False) -> Dict[str, Any]:
    """Run performance tests for voice processing."""
    command = [
        "python", "-m", "pytest",
        "tests/performance/test_voice_processing_performance.py",
        "-v" if verbose else "-q",
        "--tb=short",
        "--durations=10",
        "-m", "performance"
    ]
    
    return run_command(command, "Running Voice Processing Performance Tests")


def run_load_tests(duration: int = 60, users: int = 10) -> Dict[str, Any]:
    """Run load tests for voice processing."""
    command = [
        "locust",
        "-f", "tests/load_test/voice_processing_load_test.py",
        "--host=http://localhost:8000",
        f"-u", str(users),
        f"-r", str(min(users, 5)),
        f"-t", f"{duration}s",
        "--headless",
        "--html", "voice_load_test_report.html"
    ]
    
    return run_command(command, f"Running Voice Processing Load Tests ({users} users, {duration}s)")


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    print("🔍 Checking test dependencies...")
    
    required_packages = [
        "pytest",
        "pytest-asyncio",
        "pytest-mock",
        "locust"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies available")
    return True


def check_voice_processing_components() -> bool:
    """Check if voice processing components can be imported."""
    print("\n🔍 Checking voice processing components...")
    
    components = [
        ("app.utils.audio_processing", "AudioProcessor"),
        ("app.utils.speech_to_text", "SpeechToTextService"),
        ("app.utils.voice_config", "VoiceProcessingConfig"),
        ("app.utils.cache", "TranscriptionCache"),
        ("app.agent", "BookingAgent"),
        ("app.main", "app")
    ]
    
    for module_name, component_name in components:
        try:
            module = __import__(module_name, fromlist=[component_name])
            getattr(module, component_name)
            print(f"✅ {module_name}.{component_name}")
        except ImportError as e:
            print(f"❌ {module_name}.{component_name} - Import Error: {e}")
            return False
        except AttributeError as e:
            print(f"❌ {module_name}.{component_name} - Attribute Error: {e}")
            return False
    
    print("✅ All voice processing components available")
    return True


def generate_test_report(results: List[Dict[str, Any]]) -> None:
    """Generate comprehensive test report."""
    print("\n" + "=" * 80)
    print("📊 VOICE PROCESSING TEST REPORT")
    print("=" * 80)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r['success'])
    failed_tests = total_tests - passed_tests
    total_duration = sum(r['duration'] for r in results)
    
    print(f"Total Test Suites: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
    print(f"Total Duration: {total_duration:.1f}s")
    
    print("\n📋 Test Suite Results:")
    for result in results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"  {status} {result['description']} ({result['duration']:.1f}s)")
        
        if not result['success'] and result['stderr']:
            print(f"    Error: {result['stderr'][:100]}...")
    
    if failed_tests > 0:
        print(f"\n⚠️  {failed_tests} test suite(s) failed. Check output above for details.")
    else:
        print("\n🎉 All test suites passed!")
    
    # Performance summary
    performance_results = [r for r in results if 'performance' in r['description'].lower()]
    if performance_results:
        print("\n⚡ Performance Test Summary:")
        for result in performance_results:
            if result['success']:
                print(f"  ✅ Performance requirements met")
            else:
                print(f"  ⚠️  Performance issues detected")
    
    print("\n📁 Test Artifacts:")
    artifacts = [
        "voice_load_test_report.html",
        ".pytest_cache/",
        "__pycache__/"
    ]
    
    for artifact in artifacts:
        if os.path.exists(artifact):
            print(f"  📄 {artifact}")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Voice Processing Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--load", action="store_true", help="Run load tests only")
    parser.add_argument("--all", action="store_true", help="Run all tests (default)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--load-users", type=int, default=10, help="Number of users for load test")
    parser.add_argument("--load-duration", type=int, default=60, help="Duration for load test (seconds)")
    parser.add_argument("--skip-deps", action="store_true", help="Skip dependency checks")
    
    args = parser.parse_args()
    
    # Default to running all tests if no specific test type is specified
    if not any([args.unit, args.integration, args.performance, args.load]):
        args.all = True
    
    print("🚀 Voice Processing Test Runner")
    print("=" * 50)
    
    # Check dependencies
    if not args.skip_deps:
        if not check_dependencies():
            sys.exit(1)
        
        if not check_voice_processing_components():
            sys.exit(1)
    
    # Run tests
    results = []
    
    if args.unit or args.all:
        results.append(run_unit_tests(args.verbose))
    
    if args.integration or args.all:
        results.append(run_integration_tests(args.verbose))
    
    if args.performance or args.all:
        results.append(run_performance_tests(args.verbose))
    
    if args.load or args.all:
        results.append(run_load_tests(args.load_duration, args.load_users))
    
    # Generate report
    generate_test_report(results)
    
    # Exit with appropriate code
    failed_tests = sum(1 for r in results if not r['success'])
    if failed_tests > 0:
        print(f"\n❌ {failed_tests} test suite(s) failed")
        sys.exit(1)
    else:
        print("\n✅ All tests passed successfully!")
        sys.exit(0)


if __name__ == "__main__":
    main()
