[project]
name = "my-agent"
version = "0.1.0"
description = ""
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
dependencies = [
    # <PERSON><PERSON><PERSON><PERSON> and LangGraph dependencies
    "langchain~=0.3.14",
    "langchain-core~=0.3.9",
    "langchain-community~=0.3.17",
    "langchain-openai~=0.3.5",
    "langchain-google-genai~=2.0.7",
    "langchain-google-vertexai~=2.0.7",
    "langgraph~=0.2.63",

    # FastAPI and web framework dependencies
    "fastapi~=0.115.0",
    "uvicorn[standard]~=0.32.0",
    "pydantic~=2.10.0",
    "python-multipart~=0.0.6",  # Required for form data parsing

    # Database dependencies
    "psycopg2-binary~=2.9.9",
    "python-dotenv~=1.0.0",

    # Twilio dependencies
    "twilio~=9.3.0",

    # HTTP client dependencies
    "requests~=2.32.0",
    "httpx~=0.27.0",

    # Google Cloud and monitoring dependencies
    "google-cloud-aiplatform[evaluation,reasoningengine]~=1.87.0",
    "google-cloud-logging~=3.11.4",
    "google-cloud-speech~=2.21.0",  # Speech-to-Text API for voice message transcription
    "opentelemetry-exporter-gcp-trace~=1.9.0",
    "traceloop-sdk~=0.38.7",

    # Audio processing dependencies
    "pydub~=0.25.1",  # Audio format conversion and metadata extraction for voice messages

    # Utility dependencies
    "python-dateutil~=2.8.2"
]

requires-python = ">=3.10,<3.13"

[dependency-groups]
dev = [
    "pytest>=8.3.4",
    "pytest-asyncio>=0.23.8",
    "nest-asyncio>=1.6.0",
    "httpx>=0.27.0",  # For testing async endpoints
    "pytest-mock>=3.12.0",  # For mocking in tests
]

[project.optional-dependencies]
streamlit = [
    "streamlit~=1.42.0",
    "streamlit-extras~=0.4.3",
    "extra-streamlit-components~=0.1.71",
    "streamlit-feedback~=0.1.3",
]
jupyter = [
    "jupyter~=1.0.0",
]
lint = [
    "ruff>=0.4.6",
    "mypy~=1.15.0",
    "codespell~=2.2.0",
    "types-pyyaml~=6.0.12.20240917",
    "types-requests~=2.32.0.20240914",
]

[tool.ruff]
line-length = 88
target-version = "py310"

[tool.ruff.lint]
select = [
    "E",   # pycodestyle
    "F",   # pyflakes
    "W",   # pycodestyle warnings
    "I",   # isort
    "C",  # flake8-comprehensions
    "B",   # flake8-bugbear
    "UP", # pyupgrade
    "RUF", # ruff specific rules
]
ignore = ["E501", "C901"] # ignore line too long, too complex

[tool.ruff.lint.isort]
known-first-party = ["app", "frontend"]

[tool.mypy]
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
no_implicit_optional = true
check_untyped_defs = true
disallow_subclassing_any = true
warn_incomplete_stub = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable = true
follow_imports = "silent"
ignore_missing_imports = true
explicit_package_bases = true
disable_error_code = ["misc", "no-untyped-call", "no-any-return"]

exclude = [".venv"]

[tool.codespell]
ignore-words-list = "rouge"

skip = "./locust_env/*,uv.lock,.venv,**/*.ipynb"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.pytest.ini_options]
pythonpath = "."
asyncio_default_fixture_loop_scope = "function"

[tool.hatch.build.targets.wheel]
packages = ["app","frontend"]
