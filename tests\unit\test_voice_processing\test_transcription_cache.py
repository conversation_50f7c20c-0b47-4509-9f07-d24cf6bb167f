# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Unit tests for transcription caching system.

Tests audio file hash generation, cache operations, TTL management,
and integration with voice processing pipeline.
"""

import os
import tempfile
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from typing import Generator

import pytest

from app.utils.cache import (
    TranscriptionCache, generate_audio_file_hash,
    get_transcription_cache, get_cached_transcription,
    cache_transcription_result, get_transcription_cache_stats
)


@pytest.fixture
def temp_audio_file() -> Generator[str, None, None]:
    """Create temporary audio file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
        temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'test_audio_content')
        temp_file_path = temp_file.name
    
    yield temp_file_path
    
    if os.path.exists(temp_file_path):
        os.unlink(temp_file_path)


@pytest.fixture
def transcription_cache():
    """Create TranscriptionCache instance for testing."""
    return TranscriptionCache(ttl_minutes=5, max_entries=10)


@pytest.fixture
def sample_transcription_result():
    """Create sample transcription result for testing."""
    return {
        'success': True,
        'transcript': 'Hello, I would like to book an appointment',
        'confidence': 0.95,
        'language_code': 'en-US',
        'error_message': None,
        'metadata': {'processing_time': 2.5}
    }


class TestAudioFileHashGeneration:
    """Test cases for audio file hash generation."""

    def test_generate_audio_file_hash_success(self, temp_audio_file):
        """Test successful audio file hash generation."""
        hash_result = generate_audio_file_hash(temp_audio_file)
        
        assert isinstance(hash_result, str)
        assert len(hash_result) == 64  # SHA256 hash length
        
        # Generate hash again for same file
        hash_result2 = generate_audio_file_hash(temp_audio_file)
        assert hash_result == hash_result2  # Should be identical

    def test_generate_audio_file_hash_different_files(self):
        """Test hash generation for different files produces different hashes."""
        with tempfile.NamedTemporaryFile(delete=False) as file1:
            file1.write(b'content1')
            file1_path = file1.name
        
        with tempfile.NamedTemporaryFile(delete=False) as file2:
            file2.write(b'content2')
            file2_path = file2.name
        
        try:
            hash1 = generate_audio_file_hash(file1_path)
            hash2 = generate_audio_file_hash(file2_path)
            
            assert hash1 != hash2
        finally:
            os.unlink(file1_path)
            os.unlink(file2_path)

    def test_generate_audio_file_hash_file_not_found(self):
        """Test hash generation for non-existent file."""
        with pytest.raises(Exception) as exc_info:
            generate_audio_file_hash('/nonexistent/file.ogg')
        
        assert 'No such file or directory' in str(exc_info.value) or 'cannot find the file' in str(exc_info.value)

    def test_generate_audio_file_hash_large_file(self):
        """Test hash generation for large file (chunked reading)."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            # Write 1MB of data
            temp_file.write(b'x' * (1024 * 1024))
            temp_file_path = temp_file.name
        
        try:
            hash_result = generate_audio_file_hash(temp_file_path)
            assert isinstance(hash_result, str)
            assert len(hash_result) == 64
        finally:
            os.unlink(temp_file_path)


class TestTranscriptionCache:
    """Test cases for TranscriptionCache class."""

    def test_cache_initialization(self):
        """Test TranscriptionCache initialization."""
        cache = TranscriptionCache(ttl_minutes=30, max_entries=100)
        
        assert cache.ttl == timedelta(minutes=30)
        assert cache.max_entries == 100
        assert len(cache.cache) == 0
        
        stats = cache.get_stats()
        assert stats['hits'] == 0
        assert stats['misses'] == 0

    def test_cache_key_generation(self, transcription_cache, temp_audio_file):
        """Test cache key generation."""
        key1 = transcription_cache._generate_cache_key(temp_audio_file, 'audio/ogg', 'en-US')
        key2 = transcription_cache._generate_cache_key(temp_audio_file, 'audio/ogg', 'en-US')
        
        assert key1 == key2  # Same parameters should produce same key
        
        # Different language hint should produce different key
        key3 = transcription_cache._generate_cache_key(temp_audio_file, 'audio/ogg', 'ar-SA')
        assert key1 != key3

    def test_cache_set_and_get(self, transcription_cache, temp_audio_file, sample_transcription_result):
        """Test basic cache set and get operations."""
        # Cache miss initially
        result = transcription_cache.get(temp_audio_file, 'audio/ogg', 'en-US')
        assert result is None
        
        # Set cache entry
        transcription_cache.set(temp_audio_file, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
        
        # Cache hit
        result = transcription_cache.get(temp_audio_file, 'audio/ogg', 'en-US')
        assert result is not None
        assert result['transcript'] == 'Hello, I would like to book an appointment'
        assert result['confidence'] == 0.95

    def test_cache_ttl_expiration(self, temp_audio_file, sample_transcription_result):
        """Test cache TTL expiration."""
        # Create cache with very short TTL
        cache = TranscriptionCache(ttl_minutes=0.01)  # 0.6 seconds
        
        # Set cache entry
        cache.set(temp_audio_file, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
        
        # Should be available immediately
        result = cache.get(temp_audio_file, 'audio/ogg', 'en-US')
        assert result is not None
        
        # Wait for expiration
        time.sleep(1)
        
        # Should be expired and removed
        result = cache.get(temp_audio_file, 'audio/ogg', 'en-US')
        assert result is None

    def test_cache_max_entries_cleanup(self, temp_audio_file, sample_transcription_result):
        """Test cache cleanup when max entries exceeded."""
        cache = TranscriptionCache(ttl_minutes=60, max_entries=3)
        
        # Add entries up to max
        for i in range(5):
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(f'content{i}'.encode())
                temp_file_path = temp_file.name
            
            try:
                cache.set(temp_file_path, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
            finally:
                os.unlink(temp_file_path)
        
        # Cache should have been cleaned up
        assert len(cache.cache) <= cache.max_entries

    def test_cache_statistics(self, transcription_cache, temp_audio_file, sample_transcription_result):
        """Test cache statistics tracking."""
        # Initial stats
        stats = transcription_cache.get_stats()
        assert stats['hits'] == 0
        assert stats['misses'] == 0
        
        # Cache miss
        transcription_cache.get(temp_audio_file, 'audio/ogg', 'en-US')
        stats = transcription_cache.get_stats()
        assert stats['misses'] == 1
        
        # Set cache entry
        transcription_cache.set(temp_audio_file, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
        
        # Cache hit
        transcription_cache.get(temp_audio_file, 'audio/ogg', 'en-US')
        stats = transcription_cache.get_stats()
        assert stats['hits'] == 1
        assert stats['api_calls_saved'] == 1

    def test_cache_clear(self, transcription_cache, temp_audio_file, sample_transcription_result):
        """Test cache clearing."""
        # Add cache entry
        transcription_cache.set(temp_audio_file, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
        assert len(transcription_cache.cache) > 0
        
        # Clear cache
        transcription_cache.clear()
        assert len(transcription_cache.cache) == 0

    def test_cache_cleanup_expired(self, temp_audio_file, sample_transcription_result):
        """Test manual cleanup of expired entries."""
        cache = TranscriptionCache(ttl_minutes=0.01)  # Very short TTL
        
        # Add entry
        cache.set(temp_audio_file, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
        assert len(cache.cache) == 1
        
        # Wait for expiration
        time.sleep(1)
        
        # Manual cleanup
        cache.cleanup_expired()
        assert len(cache.cache) == 0


class TestConvenienceFunctions:
    """Test cases for transcription cache convenience functions."""

    @patch('app.utils.cache.get_transcription_cache')
    def test_get_cached_transcription(self, mock_get_cache, temp_audio_file, sample_transcription_result):
        """Test get_cached_transcription convenience function."""
        mock_cache = Mock()
        mock_cache.get.return_value = sample_transcription_result
        mock_get_cache.return_value = mock_cache
        
        result = get_cached_transcription(temp_audio_file, 'audio/ogg', 'en-US')
        
        assert result == sample_transcription_result
        mock_cache.get.assert_called_once_with(temp_audio_file, 'audio/ogg', 'en-US')

    @patch('app.utils.cache.get_transcription_cache')
    def test_cache_transcription_result(self, mock_get_cache, temp_audio_file, sample_transcription_result):
        """Test cache_transcription_result convenience function."""
        mock_cache = Mock()
        mock_get_cache.return_value = mock_cache
        
        cache_transcription_result(temp_audio_file, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
        
        mock_cache.set.assert_called_once_with(temp_audio_file, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')

    @patch('app.utils.cache.get_transcription_cache')
    def test_get_transcription_cache_stats(self, mock_get_cache):
        """Test get_transcription_cache_stats convenience function."""
        mock_cache = Mock()
        mock_stats = {'hits': 5, 'misses': 2, 'hit_rate_percent': 71.43}
        mock_cache.get_stats.return_value = mock_stats
        mock_get_cache.return_value = mock_cache
        
        stats = get_transcription_cache_stats()
        
        assert stats == mock_stats
        mock_cache.get_stats.assert_called_once()

    @patch('app.utils.cache.get_transcription_cache')
    def test_convenience_functions_error_handling(self, mock_get_cache, temp_audio_file):
        """Test error handling in convenience functions."""
        mock_get_cache.side_effect = Exception("Cache error")
        
        # Should not raise exception, should return None/handle gracefully
        result = get_cached_transcription(temp_audio_file, 'audio/ogg', 'en-US')
        assert result is None
        
        # Should not raise exception
        cache_transcription_result(temp_audio_file, 'audio/ogg', {}, 2.5, 'en-US')
        
        # Should return error info
        stats = get_transcription_cache_stats()
        assert 'error' in stats
        assert stats['cache_available'] is False


class TestTranscriptionCacheIntegration:
    """Integration tests for transcription cache with voice processing."""

    @patch('app.utils.voice_config.get_voice_config')
    def test_get_transcription_cache_with_config(self, mock_get_config):
        """Test transcription cache initialization with voice config."""
        mock_config = Mock()
        mock_config.transcription_cache_ttl_minutes = 120
        mock_get_config.return_value = mock_config
        
        cache = get_transcription_cache()
        
        assert isinstance(cache, TranscriptionCache)
        assert cache.ttl == timedelta(minutes=120)

    @patch('app.utils.voice_config.get_voice_config')
    def test_get_transcription_cache_config_error(self, mock_get_config):
        """Test transcription cache initialization when config fails."""
        mock_get_config.side_effect = Exception("Config error")
        
        cache = get_transcription_cache()
        
        # Should fallback to default configuration
        assert isinstance(cache, TranscriptionCache)
        assert cache.ttl == timedelta(minutes=60)  # Default TTL

    def test_cache_integration_with_real_files(self, sample_transcription_result):
        """Test cache integration with real file operations."""
        cache = TranscriptionCache(ttl_minutes=60, max_entries=10)
        
        # Create two different audio files
        with tempfile.NamedTemporaryFile(delete=False, suffix='.ogg') as file1:
            file1.write(b'audio_content_1')
            file1_path = file1.name
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.ogg') as file2:
            file2.write(b'audio_content_2')
            file2_path = file2.name
        
        try:
            # Cache results for both files
            cache.set(file1_path, 'audio/ogg', sample_transcription_result, 2.5, 'en-US')
            
            result2 = dict(sample_transcription_result)
            result2['transcript'] = 'Different transcription'
            cache.set(file2_path, 'audio/ogg', result2, 3.0, 'en-US')
            
            # Verify both are cached correctly
            cached1 = cache.get(file1_path, 'audio/ogg', 'en-US')
            cached2 = cache.get(file2_path, 'audio/ogg', 'en-US')
            
            assert cached1['transcript'] == 'Hello, I would like to book an appointment'
            assert cached2['transcript'] == 'Different transcription'
            
            # Verify cache statistics
            stats = cache.get_stats()
            assert stats['cache_size'] == 2
            assert stats['hits'] == 2
            
        finally:
            os.unlink(file1_path)
            os.unlink(file2_path)
