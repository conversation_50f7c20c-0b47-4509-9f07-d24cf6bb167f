# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Pytest configuration and shared fixtures for voice processing tests.

Provides common test fixtures, configuration, and utilities
for all voice processing test modules.
"""

import os
import tempfile
import pytest
from typing import Generator
from unittest.mock import Mock, patch

from tests.utils.voice_processing_mocks import (
    MockGoogleCloudSpeechClient,
    MockTwilioClient,
    MockAudioProcessor,
    MockSpeechToTextService,
    MockBookingAgent,
    MockVoiceConfig,
    create_temp_audio_file,
    create_mock_webhook_data,
    create_mock_transcription_scenarios
)


# Test configuration
pytest_plugins = ["pytest_asyncio"]


@pytest.fixture(scope="session")
def test_config():
    """Test configuration settings."""
    return {
        "voice_processing_enabled": True,
        "google_cloud_project_id": "test-project",
        "speech_to_text_language_code": "en-US",
        "max_audio_file_size_mb": 16,
        "transcription_cache_ttl_minutes": 60,
        "test_timeout": 30,
        "performance_threshold_ms": 10000
    }


@pytest.fixture
def clean_environment():
    """Clean test environment by removing voice processing environment variables."""
    env_vars = [
        'VOICE_PROCESSING_ENABLED',
        'GOOGLE_CLOUD_PROJECT_ID',
        'SPEECH_TO_TEXT_LANGUAGE_CODE',
        'MAX_AUDIO_FILE_SIZE_MB',
        'TRANSCRIPTION_CACHE_TTL_MINUTES'
    ]
    
    # Store original values
    original_values = {}
    for var in env_vars:
        original_values[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]
    
    yield
    
    # Restore original values
    for var, value in original_values.items():
        if value is not None:
            os.environ[var] = value
        elif var in os.environ:
            del os.environ[var]


@pytest.fixture
def temp_audio_file() -> Generator[str, None, None]:
    """Create temporary audio file for testing."""
    file_path = create_temp_audio_file()
    yield file_path
    
    # Cleanup
    if os.path.exists(file_path):
        os.unlink(file_path)


@pytest.fixture
def temp_audio_files() -> Generator[list, None, None]:
    """Create multiple temporary audio files for testing."""
    files = []
    for i in range(3):
        content = b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + f'content_{i}'.encode() * 50
        file_path = create_temp_audio_file(content, f'.ogg')
        files.append(file_path)
    
    yield files
    
    # Cleanup
    for file_path in files:
        if os.path.exists(file_path):
            os.unlink(file_path)


@pytest.fixture
def mock_google_speech_client():
    """Mock Google Cloud Speech-to-Text client."""
    return MockGoogleCloudSpeechClient()


@pytest.fixture
def mock_twilio_client():
    """Mock Twilio client for media download."""
    return MockTwilioClient()


@pytest.fixture
def mock_audio_processor():
    """Mock audio processor."""
    return MockAudioProcessor()


@pytest.fixture
def mock_speech_to_text_service():
    """Mock speech-to-text service."""
    return MockSpeechToTextService()


@pytest.fixture
def mock_booking_agent():
    """Mock booking agent."""
    return MockBookingAgent()


@pytest.fixture
def mock_voice_config():
    """Mock voice configuration."""
    return MockVoiceConfig()


@pytest.fixture
def mock_voice_config_disabled():
    """Mock voice configuration with voice processing disabled."""
    return MockVoiceConfig(voice_processing_enabled=False)


@pytest.fixture
def webhook_data():
    """Standard webhook data for testing."""
    return create_mock_webhook_data()


@pytest.fixture
def webhook_data_arabic():
    """Webhook data for Arabic voice message testing."""
    return create_mock_webhook_data(
        phone_number="+966501234567",
        media_url="https://api.twilio.com/test-arabic-audio.ogg"
    )


@pytest.fixture
def transcription_scenarios():
    """Various transcription scenarios for testing."""
    return create_mock_transcription_scenarios()


@pytest.fixture
def sample_transcription_result():
    """Sample successful transcription result."""
    return {
        'success': True,
        'transcript': 'I would like to book a massage appointment for tomorrow at 3 PM',
        'confidence': 0.95,
        'language_code': 'en-US',
        'error_message': None,
        'metadata': {'processing_time': 2.5}
    }


@pytest.fixture
def sample_voice_metadata():
    """Sample voice metadata for testing."""
    return {
        'original_media_url': 'https://api.twilio.com/test-audio.ogg',
        'media_content_type': 'audio/ogg',
        'transcription_confidence': 0.95,
        'audio_duration': 15.0,
        'processing_applied': 'converted to mono, resampled to 16000Hz'
    }


@pytest.fixture
def mock_voice_processing_pipeline():
    """Mock complete voice processing pipeline."""
    with patch('app.main.get_voice_config') as mock_get_config:
        with patch('app.main.TwilioClient') as mock_twilio_class:
            with patch('app.main.AudioProcessor') as mock_audio_processor_class:
                with patch('app.main.SpeechToTextService') as mock_speech_service_class:
                    with patch('app.main.BookingAgent') as mock_agent_class:
                        
                        # Setup mocks
                        mock_config = MockVoiceConfig()
                        mock_get_config.return_value = mock_config
                        
                        mock_twilio = MockTwilioClient()
                        mock_twilio_class.return_value = mock_twilio
                        
                        mock_processor = MockAudioProcessor()
                        mock_audio_processor_class.return_value = mock_processor
                        
                        mock_service = MockSpeechToTextService()
                        mock_speech_service_class.return_value = mock_service
                        
                        mock_agent = MockBookingAgent()
                        mock_agent_class.return_value = mock_agent
                        
                        yield {
                            'config': mock_config,
                            'twilio': mock_twilio,
                            'audio_processor': mock_processor,
                            'speech_service': mock_service,
                            'agent': mock_agent
                        }


@pytest.fixture
def performance_monitor():
    """Performance monitoring fixture for tests."""
    import time
    
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.measurements = []
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self, operation_name: str):
            if self.start_time:
                duration = time.time() - self.start_time
                self.measurements.append({
                    'operation': operation_name,
                    'duration_ms': duration * 1000
                })
                self.start_time = None
                return duration
            return 0
        
        def get_measurements(self):
            return self.measurements
        
        def assert_performance(self, operation_name: str, max_duration_ms: float):
            measurement = next(
                (m for m in self.measurements if m['operation'] == operation_name),
                None
            )
            if measurement:
                assert measurement['duration_ms'] <= max_duration_ms, \
                    f"{operation_name} took {measurement['duration_ms']:.1f}ms, should be <={max_duration_ms}ms"
    
    return PerformanceMonitor()


# Test markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "load: mark test as a load test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file paths."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        elif "load_test" in str(item.fspath):
            item.add_marker(pytest.mark.load)
        
        # Mark slow tests
        if "stress" in item.name or "load" in item.name or "concurrent" in item.name:
            item.add_marker(pytest.mark.slow)


# Cleanup hooks
@pytest.fixture(autouse=True)
def cleanup_temp_files():
    """Automatically cleanup temporary files after each test."""
    yield
    
    # Clean up any remaining temporary files
    import glob
    temp_patterns = [
        '/tmp/voice_*',
        '/tmp/audio_*',
        '/tmp/test_*',
        tempfile.gettempdir() + '/voice_*',
        tempfile.gettempdir() + '/audio_*'
    ]
    
    for pattern in temp_patterns:
        for file_path in glob.glob(pattern):
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)
            except (OSError, PermissionError):
                pass  # Ignore cleanup errors


# Test environment setup
@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment for voice processing tests."""
    # Set test environment variables
    os.environ['TESTING'] = 'true'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    yield
    
    # Cleanup test environment
    if 'TESTING' in os.environ:
        del os.environ['TESTING']
    if 'LOG_LEVEL' in os.environ:
        del os.environ['LOG_LEVEL']
