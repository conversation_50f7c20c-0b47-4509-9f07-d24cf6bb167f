# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Integration tests for end-to-end voice message processing.

Tests complete voice processing workflow from webhook to response,
including audio processing, transcription, agent integration,
and error handling scenarios.
"""

import os
import tempfile
from unittest.mock import Mock, patch, AsyncMock
from typing import Generator

import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.agent import BookingAgent
from app.utils.speech_to_text import TranscriptionResult
from app.utils.audio_processing import AudioMetadata, AudioProcessingResult


@pytest.fixture
def client():
    """Create FastAPI test client."""
    return TestClient(app)


@pytest.fixture
def temp_audio_file() -> Generator[str, None, None]:
    """Create temporary audio file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
        # Write minimal OGG content for testing
        temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'test_audio_content' * 100)
        temp_file_path = temp_file.name
    
    yield temp_file_path
    
    if os.path.exists(temp_file_path):
        os.unlink(temp_file_path)


@pytest.fixture
def mock_twilio_download():
    """Mock Twilio media download."""
    def _mock_download(media_url, file_path):
        # Simulate downloading audio file
        with open(file_path, 'wb') as f:
            f.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'downloaded_audio_content' * 50)
        return True
    
    return _mock_download


@pytest.fixture
def mock_successful_transcription():
    """Mock successful transcription result."""
    return TranscriptionResult(
        success=True,
        transcript="I would like to book a massage appointment for tomorrow at 3 PM",
        confidence=0.95,
        language_code="en-US",
        processing_time=2.5
    )


@pytest.fixture
def mock_audio_metadata():
    """Mock audio metadata."""
    return AudioMetadata(
        file_size=1024 * 50,  # 50KB
        duration_seconds=15.0,
        content_type='audio/ogg',
        sample_rate=16000,
        channels=1
    )


@pytest.fixture
def mock_audio_processing_result():
    """Mock audio processing result."""
    return AudioProcessingResult(
        success=True,
        format_valid=True,
        processed_file_path='/tmp/processed_audio.ogg',
        processing_applied='converted to mono, resampled to 16000Hz',
        quality_score=0.85
    )


class TestVoiceProcessingWebhook:
    """Test cases for voice processing webhook integration."""

    @patch('app.main.get_voice_config')
    @patch('app.main.TwilioClient')
    @patch('app.main.AudioProcessor')
    @patch('app.main.SpeechToTextService')
    @patch('app.main.BookingAgent')
    def test_voice_message_processing_success(
        self, mock_agent_class, mock_speech_service_class, mock_audio_processor_class,
        mock_twilio_class, mock_get_config, client, mock_successful_transcription,
        mock_audio_metadata, mock_audio_processing_result
    ):
        """Test successful end-to-end voice message processing."""
        # Mock voice config
        mock_config = Mock()
        mock_config.voice_processing_enabled = True
        mock_config.is_voice_processing_available.return_value = True
        mock_get_config.return_value = mock_config
        
        # Mock Twilio client
        mock_twilio = Mock()
        mock_twilio.download_media.return_value = True
        mock_twilio_class.return_value = mock_twilio
        
        # Mock audio processor
        mock_processor = Mock()
        mock_processor.validate_audio_format.return_value = mock_audio_processing_result
        mock_processor.extract_metadata.return_value = AudioProcessingResult(
            success=True, metadata=mock_audio_metadata
        )
        mock_processor.preprocess_for_transcription.return_value = mock_audio_processing_result
        mock_audio_processor_class.return_value = mock_processor
        
        # Mock speech-to-text service
        mock_speech_service = Mock()
        mock_speech_service.transcribe_audio.return_value = mock_successful_transcription
        mock_speech_service_class.return_value = mock_speech_service
        
        # Mock booking agent
        mock_agent = Mock()
        mock_agent.process_voice_message.return_value = "Thank you! I've noted your request for a massage appointment tomorrow at 3 PM. Let me check availability and get back to you."
        mock_agent_class.return_value = mock_agent
        
        # Test voice message webhook
        response = client.post("/webhook", data={
            "From": "whatsapp:+1234567890",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test-audio.ogg",
            "MediaContentType0": "audio/ogg"
        })
        
        assert response.status_code == 200
        assert "massage appointment" in response.text
        
        # Verify all components were called
        mock_twilio.download_media.assert_called_once()
        mock_processor.validate_audio_format.assert_called_once()
        mock_speech_service.transcribe_audio.assert_called_once()
        mock_agent.process_voice_message.assert_called_once()

    @patch('app.main.get_voice_config')
    def test_voice_processing_disabled(self, mock_get_config, client):
        """Test voice message handling when voice processing is disabled."""
        # Mock voice config with processing disabled
        mock_config = Mock()
        mock_config.voice_processing_enabled = False
        mock_config.is_voice_processing_available.return_value = False
        mock_get_config.return_value = mock_config
        
        response = client.post("/webhook", data={
            "From": "whatsapp:+1234567890",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test-audio.ogg",
            "MediaContentType0": "audio/ogg"
        })
        
        assert response.status_code == 200
        assert "voice message" in response.text.lower()
        assert "text message" in response.text.lower()

    @patch('app.main.get_voice_config')
    @patch('app.main.TwilioClient')
    def test_voice_processing_download_failure(self, mock_twilio_class, mock_get_config, client):
        """Test voice processing when media download fails."""
        # Mock voice config
        mock_config = Mock()
        mock_config.voice_processing_enabled = True
        mock_config.is_voice_processing_available.return_value = True
        mock_get_config.return_value = mock_config
        
        # Mock Twilio client with download failure
        mock_twilio = Mock()
        mock_twilio.download_media.return_value = False
        mock_twilio_class.return_value = mock_twilio
        
        response = client.post("/webhook", data={
            "From": "whatsapp:+1234567890",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test-audio.ogg",
            "MediaContentType0": "audio/ogg"
        })
        
        assert response.status_code == 200
        assert "unable to process" in response.text.lower() or "try again" in response.text.lower()

    @patch('app.main.get_voice_config')
    @patch('app.main.TwilioClient')
    @patch('app.main.AudioProcessor')
    def test_voice_processing_invalid_audio_format(
        self, mock_audio_processor_class, mock_twilio_class, mock_get_config, client
    ):
        """Test voice processing with invalid audio format."""
        # Mock voice config
        mock_config = Mock()
        mock_config.voice_processing_enabled = True
        mock_config.is_voice_processing_available.return_value = True
        mock_get_config.return_value = mock_config
        
        # Mock Twilio client
        mock_twilio = Mock()
        mock_twilio.download_media.return_value = True
        mock_twilio_class.return_value = mock_twilio
        
        # Mock audio processor with format validation failure
        mock_processor = Mock()
        mock_processor.validate_audio_format.return_value = AudioProcessingResult(
            success=False,
            error_message="Unsupported audio format: audio/wav"
        )
        mock_audio_processor_class.return_value = mock_processor
        
        response = client.post("/webhook", data={
            "From": "whatsapp:+1234567890",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test-audio.wav",
            "MediaContentType0": "audio/wav"
        })
        
        assert response.status_code == 200
        assert "unable to process" in response.text.lower()


class TestVoiceProcessingAgent:
    """Test cases for voice processing agent integration."""

    @patch('app.agent.get_booking_context')
    @patch('app.agent.save_booking_context')
    def test_agent_voice_message_processing(self, mock_save_context, mock_get_context):
        """Test BookingAgent voice message processing."""
        # Mock booking context
        mock_get_context.return_value = {
            'conversation_history': [],
            'client_name': None,
            'phone_number': '+1234567890'
        }
        
        agent = BookingAgent()
        
        voice_metadata = {
            'original_media_url': 'https://api.twilio.com/test-audio.ogg',
            'media_content_type': 'audio/ogg',
            'transcription_confidence': 0.95,
            'audio_duration': 15.0
        }
        
        response = agent.process_voice_message(
            "I need to book a facial appointment for next Tuesday at 2 PM",
            "+1234567890",
            voice_metadata
        )
        
        assert isinstance(response, str)
        assert len(response) > 0
        
        # Verify context was saved
        mock_save_context.assert_called_once()
        
        # Verify voice message was added to conversation history
        call_args = mock_save_context.call_args[0]
        context = call_args[1]
        assert 'conversation_history' in context
        
        # Find voice message in conversation history
        voice_messages = [
            msg for msg in context['conversation_history']
            if msg.get('message_type') == 'voice'
        ]
        assert len(voice_messages) > 0
        assert voice_messages[0]['content'] == "I need to book a facial appointment for next Tuesday at 2 PM"
        assert voice_messages[0]['voice_metadata'] == voice_metadata

    def test_agent_voice_response_enhancement(self):
        """Test voice-specific response enhancement."""
        agent = BookingAgent()
        
        # Test low confidence enhancement
        low_confidence_response = agent._enhance_voice_response(
            "I've booked your appointment.",
            confidence=0.6,
            language_code="en-US"
        )
        
        assert "I understood" in low_confidence_response or "heard" in low_confidence_response
        
        # Test high confidence (no enhancement needed)
        high_confidence_response = agent._enhance_voice_response(
            "I've booked your appointment.",
            confidence=0.95,
            language_code="en-US"
        )
        
        assert high_confidence_response == "I've booked your appointment."


class TestVoiceProcessingPerformance:
    """Test cases for voice processing performance requirements."""

    @patch('app.main.get_voice_config')
    @patch('app.main.TwilioClient')
    @patch('app.main.AudioProcessor')
    @patch('app.main.SpeechToTextService')
    @patch('app.main.BookingAgent')
    @patch('time.time')
    def test_voice_processing_performance_threshold(
        self, mock_time, mock_agent_class, mock_speech_service_class,
        mock_audio_processor_class, mock_twilio_class, mock_get_config, client,
        mock_successful_transcription, mock_audio_metadata, mock_audio_processing_result
    ):
        """Test that voice processing meets performance requirements (<10 seconds)."""
        # Mock time progression
        time_sequence = [0, 1, 2, 3, 4, 5]  # Total 5 seconds
        mock_time.side_effect = time_sequence
        
        # Setup mocks (same as success test)
        mock_config = Mock()
        mock_config.voice_processing_enabled = True
        mock_config.is_voice_processing_available.return_value = True
        mock_get_config.return_value = mock_config
        
        mock_twilio = Mock()
        mock_twilio.download_media.return_value = True
        mock_twilio_class.return_value = mock_twilio
        
        mock_processor = Mock()
        mock_processor.validate_audio_format.return_value = mock_audio_processing_result
        mock_processor.extract_metadata.return_value = AudioProcessingResult(
            success=True, metadata=mock_audio_metadata
        )
        mock_processor.preprocess_for_transcription.return_value = mock_audio_processing_result
        mock_audio_processor_class.return_value = mock_processor
        
        mock_speech_service = Mock()
        mock_speech_service.transcribe_audio.return_value = mock_successful_transcription
        mock_speech_service_class.return_value = mock_speech_service
        
        mock_agent = Mock()
        mock_agent.process_voice_message.return_value = "Appointment booked successfully!"
        mock_agent_class.return_value = mock_agent
        
        response = client.post("/webhook", data={
            "From": "whatsapp:+1234567890",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test-audio.ogg",
            "MediaContentType0": "audio/ogg"
        })
        
        assert response.status_code == 200
        # Total processing time should be under 10 seconds (we mocked 5 seconds)
        assert len(time_sequence) <= 10


class TestVoiceProcessingErrorHandling:
    """Test cases for voice processing error handling and fallback mechanisms."""

    @patch('app.main.get_voice_config')
    @patch('app.main.TwilioClient')
    @patch('app.main.AudioProcessor')
    @patch('app.main.SpeechToTextService')
    def test_transcription_failure_fallback(
        self, mock_speech_service_class, mock_audio_processor_class,
        mock_twilio_class, mock_get_config, client, mock_audio_processing_result
    ):
        """Test fallback mechanism when transcription fails."""
        # Setup mocks
        mock_config = Mock()
        mock_config.voice_processing_enabled = True
        mock_config.is_voice_processing_available.return_value = True
        mock_get_config.return_value = mock_config
        
        mock_twilio = Mock()
        mock_twilio.download_media.return_value = True
        mock_twilio_class.return_value = mock_twilio
        
        mock_processor = Mock()
        mock_processor.validate_audio_format.return_value = mock_audio_processing_result
        mock_processor.extract_metadata.return_value = AudioProcessingResult(success=True)
        mock_processor.preprocess_for_transcription.return_value = mock_audio_processing_result
        mock_audio_processor_class.return_value = mock_processor
        
        # Mock transcription failure
        mock_speech_service = Mock()
        mock_speech_service.transcribe_audio.return_value = TranscriptionResult(
            success=False,
            error_message="Speech-to-Text API error"
        )
        mock_speech_service_class.return_value = mock_speech_service
        
        response = client.post("/webhook", data={
            "From": "whatsapp:+1234567890",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test-audio.ogg",
            "MediaContentType0": "audio/ogg"
        })
        
        assert response.status_code == 200
        assert "unable to process" in response.text.lower() or "try again" in response.text.lower()

    @patch('app.main.get_voice_config')
    def test_voice_processing_exception_handling(self, mock_get_config, client):
        """Test exception handling in voice processing pipeline."""
        # Mock config that raises exception
        mock_get_config.side_effect = Exception("Configuration error")
        
        response = client.post("/webhook", data={
            "From": "whatsapp:+1234567890",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test-audio.ogg",
            "MediaContentType0": "audio/ogg"
        })
        
        # Should still return 200 with fallback message
        assert response.status_code == 200
        assert "text message" in response.text.lower()
