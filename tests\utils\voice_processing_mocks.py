# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Mock utilities for voice processing tests.

Provides reusable mocks for external services and components
used in voice message processing tests.
"""

import os
import tempfile
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, Optional, List

from app.utils.speech_to_text import TranscriptionResult
from app.utils.audio_processing import AudioMetadata, AudioProcessingResult
from app.utils.voice_config import VoiceProcessingConfig


class MockGoogleCloudSpeechClient:
    """Mock Google Cloud Speech-to-Text client for testing."""
    
    def __init__(self, responses: Optional[List[Dict[str, Any]]] = None):
        """
        Initialize mock client with predefined responses.
        
        Args:
            responses: List of response configurations for different scenarios
        """
        self.responses = responses or [
            {
                'transcript': 'I would like to book an appointment',
                'confidence': 0.95,
                'language_code': 'en-US'
            }
        ]
        self.call_count = 0
        self.recognize = Mock(side_effect=self._mock_recognize)
    
    def _mock_recognize(self, request):
        """Mock recognize method with realistic responses."""
        response_config = self.responses[self.call_count % len(self.responses)]
        self.call_count += 1
        
        # Create mock response structure
        mock_response = Mock()
        mock_alternative = Mock()
        mock_alternative.transcript = response_config['transcript']
        mock_alternative.confidence = response_config['confidence']
        
        mock_result = Mock()
        mock_result.alternatives = [mock_alternative]
        mock_response.results = [mock_result]
        
        return mock_response


class MockTwilioClient:
    """Mock Twilio client for testing media download."""
    
    def __init__(self, download_success: bool = True, audio_content: bytes = None):
        """
        Initialize mock Twilio client.
        
        Args:
            download_success: Whether media download should succeed
            audio_content: Content to write to downloaded file
        """
        self.download_success = download_success
        self.audio_content = audio_content or (
            b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'mock_audio_content' * 50
        )
        self.download_calls = []
    
    def download_media(self, media_url: str, file_path: str) -> bool:
        """Mock media download method."""
        self.download_calls.append({'media_url': media_url, 'file_path': file_path})
        
        if self.download_success:
            # Write mock audio content to file
            with open(file_path, 'wb') as f:
                f.write(self.audio_content)
            return True
        else:
            return False


class MockAudioProcessor:
    """Mock audio processor for testing."""
    
    def __init__(self, 
                 validation_success: bool = True,
                 metadata_success: bool = True,
                 preprocessing_success: bool = True):
        """
        Initialize mock audio processor.
        
        Args:
            validation_success: Whether audio validation should succeed
            metadata_success: Whether metadata extraction should succeed
            preprocessing_success: Whether preprocessing should succeed
        """
        self.validation_success = validation_success
        self.metadata_success = metadata_success
        self.preprocessing_success = preprocessing_success
        self.validation_calls = []
        self.metadata_calls = []
        self.preprocessing_calls = []
    
    def validate_audio_format(self, file_path: str, content_type: str) -> AudioProcessingResult:
        """Mock audio format validation."""
        self.validation_calls.append({'file_path': file_path, 'content_type': content_type})
        
        if self.validation_success:
            return AudioProcessingResult(
                success=True,
                format_valid=True,
                content_type=content_type,
                file_path=file_path
            )
        else:
            return AudioProcessingResult(
                success=False,
                format_valid=False,
                error_message=f"Unsupported audio format: {content_type}"
            )
    
    def extract_metadata(self, file_path: str, content_type: str) -> AudioProcessingResult:
        """Mock metadata extraction."""
        self.metadata_calls.append({'file_path': file_path, 'content_type': content_type})
        
        if self.metadata_success:
            metadata = AudioMetadata(
                file_size=os.path.getsize(file_path) if os.path.exists(file_path) else 1024,
                duration_seconds=15.0,
                content_type=content_type,
                sample_rate=16000,
                channels=1,
                file_path=file_path
            )
            return AudioProcessingResult(success=True, metadata=metadata)
        else:
            return AudioProcessingResult(
                success=False,
                error_message="Failed to extract audio metadata"
            )
    
    def preprocess_for_transcription(self, file_path: str, content_type: str) -> AudioProcessingResult:
        """Mock audio preprocessing."""
        self.preprocessing_calls.append({'file_path': file_path, 'content_type': content_type})
        
        if self.preprocessing_success:
            return AudioProcessingResult(
                success=True,
                processed_file_path=file_path,
                processing_applied="converted to mono, resampled to 16000Hz",
                quality_score=0.85
            )
        else:
            return AudioProcessingResult(
                success=False,
                error_message="Audio preprocessing failed"
            )


class MockSpeechToTextService:
    """Mock speech-to-text service for testing."""
    
    def __init__(self, transcription_results: Optional[List[TranscriptionResult]] = None):
        """
        Initialize mock speech-to-text service.
        
        Args:
            transcription_results: List of transcription results to return
        """
        self.transcription_results = transcription_results or [
            TranscriptionResult(
                success=True,
                transcript="I would like to book a massage appointment for tomorrow at 3 PM",
                confidence=0.95,
                language_code="en-US",
                processing_time=2.5
            )
        ]
        self.call_count = 0
        self.transcription_calls = []
    
    def transcribe_audio(self, file_path: str, content_type: str, language_hint: Optional[str] = None) -> TranscriptionResult:
        """Mock audio transcription."""
        self.transcription_calls.append({
            'file_path': file_path,
            'content_type': content_type,
            'language_hint': language_hint
        })
        
        result = self.transcription_results[self.call_count % len(self.transcription_results)]
        self.call_count += 1
        return result
    
    def get_supported_formats(self) -> List[str]:
        """Mock supported formats."""
        return ['audio/ogg', 'audio/mpeg', 'audio/mp4', 'audio/3gpp', 'audio/amr']
    
    def is_format_supported(self, content_type: str) -> bool:
        """Mock format support check."""
        return content_type in self.get_supported_formats()


class MockBookingAgent:
    """Mock booking agent for testing."""
    
    def __init__(self, responses: Optional[List[str]] = None):
        """
        Initialize mock booking agent.
        
        Args:
            responses: List of responses to return for voice messages
        """
        self.responses = responses or [
            "Thank you! I've noted your request for a massage appointment tomorrow at 3 PM. Let me check availability and get back to you.",
            "I understand you'd like to book an appointment. Could you please specify which service you're interested in?",
            "Your appointment has been scheduled successfully. You'll receive a confirmation shortly."
        ]
        self.call_count = 0
        self.voice_message_calls = []
    
    def process_voice_message(self, transcribed_text: str, phone_number: str, voice_metadata: Dict = None) -> str:
        """Mock voice message processing."""
        self.voice_message_calls.append({
            'transcribed_text': transcribed_text,
            'phone_number': phone_number,
            'voice_metadata': voice_metadata
        })
        
        response = self.responses[self.call_count % len(self.responses)]
        self.call_count += 1
        return response
    
    def _enhance_voice_response(self, response: str, confidence: float, language_code: str) -> str:
        """Mock voice response enhancement."""
        if confidence < 0.7:
            return f"I understood: {response}. Please let me know if I got that right."
        return response


class MockVoiceConfig:
    """Mock voice configuration for testing."""
    
    def __init__(self, 
                 voice_processing_enabled: bool = True,
                 google_cloud_project_id: str = "test-project",
                 speech_to_text_language_code: str = "en-US",
                 max_audio_file_size_mb: int = 16,
                 transcription_cache_ttl_minutes: int = 60):
        """Initialize mock voice configuration."""
        self.voice_processing_enabled = voice_processing_enabled
        self.google_cloud_project_id = google_cloud_project_id
        self.speech_to_text_language_code = speech_to_text_language_code
        self.max_audio_file_size_mb = max_audio_file_size_mb
        self.transcription_cache_ttl_minutes = transcription_cache_ttl_minutes
    
    def is_voice_processing_available(self) -> bool:
        """Mock voice processing availability check."""
        return self.voice_processing_enabled and self.google_cloud_project_id is not None
    
    def validate(self):
        """Mock configuration validation."""
        if self.voice_processing_enabled and not self.google_cloud_project_id:
            raise Exception("GOOGLE_CLOUD_PROJECT_ID is required when voice processing is enabled")
    
    def to_dict(self) -> Dict[str, Any]:
        """Mock configuration serialization."""
        return {
            'voice_processing_enabled': self.voice_processing_enabled,
            'google_cloud_project_id': 'configured' if self.google_cloud_project_id else None,
            'speech_to_text_language_code': self.speech_to_text_language_code,
            'max_audio_file_size_mb': self.max_audio_file_size_mb,
            'transcription_cache_ttl_minutes': self.transcription_cache_ttl_minutes,
            'is_available': self.is_voice_processing_available()
        }


def create_temp_audio_file(content: bytes = None, suffix: str = '.ogg') -> str:
    """
    Create temporary audio file for testing.
    
    Args:
        content: Audio content to write to file
        suffix: File extension
        
    Returns:
        Path to temporary audio file
    """
    if content is None:
        content = b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'test_audio_content' * 50
    
    temp_file = tempfile.NamedTemporaryFile(suffix=suffix, delete=False)
    temp_file.write(content)
    temp_file.close()
    return temp_file.name


def create_mock_webhook_data(phone_number: str = "+1234567890",
                           media_url: str = "https://api.twilio.com/test-audio.ogg",
                           content_type: str = "audio/ogg") -> Dict[str, str]:
    """
    Create mock webhook data for testing.
    
    Args:
        phone_number: Phone number for the message
        media_url: URL of the audio file
        content_type: MIME type of the audio file
        
    Returns:
        Dictionary with webhook form data
    """
    return {
        "From": f"whatsapp:{phone_number}",
        "Body": "",
        "NumMedia": "1",
        "MediaUrl0": media_url,
        "MediaContentType0": content_type,
        "MessageSid": "SM1234567890abcdef1234567890abcdef",
        "AccountSid": "AC1234567890abcdef1234567890abcdef"
    }


def create_mock_transcription_scenarios() -> List[TranscriptionResult]:
    """
    Create various transcription scenarios for testing.
    
    Returns:
        List of TranscriptionResult objects for different test scenarios
    """
    return [
        # Successful booking request
        TranscriptionResult(
            success=True,
            transcript="I would like to book a massage appointment for tomorrow at 3 PM",
            confidence=0.95,
            language_code="en-US",
            processing_time=2.5
        ),
        # Low confidence transcription
        TranscriptionResult(
            success=True,
            transcript="I need appointment booking",
            confidence=0.65,
            language_code="en-US",
            processing_time=3.0
        ),
        # Arabic transcription
        TranscriptionResult(
            success=True,
            transcript="أريد حجز موعد للمساج غداً في الساعة الثالثة",
            confidence=0.88,
            language_code="ar-SA",
            processing_time=2.8
        ),
        # Transcription failure
        TranscriptionResult(
            success=False,
            error_message="Speech-to-Text API error: Unable to process audio",
            processing_time=1.0
        ),
        # Complex booking request
        TranscriptionResult(
            success=True,
            transcript="Hi, I need to book a facial appointment for next Tuesday morning, preferably around 10 AM, and I also want to know if you have any packages available",
            confidence=0.92,
            language_code="en-US",
            processing_time=4.2
        )
    ]
