"""
Date parsing utilities for handling relative and absolute date expressions.

This module provides robust date parsing functionality for the WhatsApp booking agent,
handling both relative expressions like "tomorrow" and "next Sunday" as well as
absolute dates, ensuring all dates use the correct current year.
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from dateutil import parser as dateutil_parser
from dateutil.relativedelta import relativedelta

from app.utils.exceptions import ValidationException, ErrorContext, log_exception

logger = logging.getLogger(__name__)

# Common relative date expressions in English and Arabic
# Order matters - longer patterns first to avoid partial matches
RELATIVE_DATE_PATTERNS = [
    # English patterns (longer first)
    ('day after tomorrow', {'days': 2}),
    ('next week', {'weeks': 1}),
    ('next month', {'months': 1}),
    ('tomorrow', {'days': 1}),
    ('today', {'days': 0}),

    # Arabic patterns (longer first)
    ('الأسبوع القادم', {'weeks': 1}),
    ('الشهر القادم', {'months': 1}),
    ('بعد غد', {'days': 2}),
    ('غدا', {'days': 1}),
    ('اليوم', {'days': 0}),
]

# Day name patterns for "next [day]" expressions
WEEKDAY_PATTERNS = {
    # English
    'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
    'friday': 4, 'saturday': 5, 'sunday': 6,
    
    # Arabic
    'الاثنين': 0, 'الثلاثاء': 1, 'الأربعاء': 2, 'الخميس': 3,
    'الجمعة': 4, 'السبت': 5, 'الأحد': 6,
}


def parse_relative_date(text: str, reference_date: datetime = None) -> Optional[str]:
    """
    Parse relative date expressions and convert them to YYYY-MM-DD format.
    
    Args:
        text: Input text containing date expression
        reference_date: Reference date for calculations (defaults to current date)
        
    Returns:
        Date string in YYYY-MM-DD format or None if no date found
        
    Raises:
        Uses Task 2.1 error handling framework for validation errors
    """
    try:
        if reference_date is None:
            reference_date = datetime.now()
            
        text_lower = text.lower().strip()
        logger.debug(f"Parsing relative date from text: '{text}' with reference: {reference_date.date()}")
        
        # Check for direct relative patterns (longer patterns first)
        for pattern, delta_kwargs in RELATIVE_DATE_PATTERNS:
            if pattern in text_lower:
                result_date = reference_date + relativedelta(**delta_kwargs)
                formatted_date = result_date.strftime('%Y-%m-%d')
                logger.info(f"Parsed relative date '{pattern}' → {formatted_date}")
                return formatted_date
        
        # Check for "next [weekday]" patterns
        next_weekday_date = _parse_next_weekday(text_lower, reference_date)
        if next_weekday_date:
            return next_weekday_date
            
        # Check for "in X days/weeks" patterns
        duration_date = _parse_duration_expressions(text_lower, reference_date)
        if duration_date:
            return duration_date
            
        # Try to parse with dateutil for more complex expressions
        dateutil_date = _parse_with_dateutil(text, reference_date)
        if dateutil_date:
            return dateutil_date
            
        logger.debug(f"No relative date pattern found in: '{text}'")
        return None
        
    except Exception as e:
        # Use Task 2.1 error handling
        context = ErrorContext(
            operation="parse_relative_date",
            additional_data={
                "input_text": text[:100],  # Limit text length for logging
                "reference_date": reference_date.isoformat() if reference_date else None
            }
        )
        log_exception(e, context=context)
        return None


def _parse_next_weekday(text: str, reference_date: datetime) -> Optional[str]:
    """Parse 'next [weekday]' expressions."""
    try:
        # Look for "next" followed by a weekday
        next_pattern = r'next\s+(\w+)'
        match = re.search(next_pattern, text)
        
        if match:
            weekday_name = match.group(1).lower()
            if weekday_name in WEEKDAY_PATTERNS:
                target_weekday = WEEKDAY_PATTERNS[weekday_name]
                current_weekday = reference_date.weekday()
                
                # Calculate days until next occurrence of the weekday
                days_ahead = target_weekday - current_weekday
                if days_ahead <= 0:  # Target day already happened this week
                    days_ahead += 7
                    
                result_date = reference_date + timedelta(days=days_ahead)
                formatted_date = result_date.strftime('%Y-%m-%d')
                logger.info(f"Parsed next weekday 'next {weekday_name}' → {formatted_date}")
                return formatted_date
                
        # Check Arabic patterns for "next [weekday]"
        for arabic_day, weekday_num in WEEKDAY_PATTERNS.items():
            if arabic_day in text and ('القادم' in text or 'المقبل' in text):
                current_weekday = reference_date.weekday()
                days_ahead = weekday_num - current_weekday
                if days_ahead <= 0:
                    days_ahead += 7
                    
                result_date = reference_date + timedelta(days=days_ahead)
                formatted_date = result_date.strftime('%Y-%m-%d')
                logger.info(f"Parsed Arabic next weekday '{arabic_day}' → {formatted_date}")
                return formatted_date
                
        return None
        
    except Exception as e:
        logger.error(f"Error parsing next weekday from '{text}': {e}")
        return None


def _parse_duration_expressions(text: str, reference_date: datetime) -> Optional[str]:
    """Parse 'in X days/weeks' expressions."""
    try:
        # Pattern for "in X days/weeks/months"
        duration_pattern = r'in\s+(\d+)\s+(day|week|month)s?'
        match = re.search(duration_pattern, text)
        
        if match:
            amount = int(match.group(1))
            unit = match.group(2)
            
            if unit == 'day':
                result_date = reference_date + timedelta(days=amount)
            elif unit == 'week':
                result_date = reference_date + timedelta(weeks=amount)
            elif unit == 'month':
                result_date = reference_date + relativedelta(months=amount)
            else:
                return None
                
            formatted_date = result_date.strftime('%Y-%m-%d')
            logger.info(f"Parsed duration expression 'in {amount} {unit}s' → {formatted_date}")
            return formatted_date
            
        return None
        
    except Exception as e:
        logger.error(f"Error parsing duration expression from '{text}': {e}")
        return None


def _parse_with_dateutil(text: str, reference_date: datetime) -> Optional[str]:
    """Parse dates using dateutil parser with current year context."""
    try:
        # Extract potential date strings from text
        date_candidates = _extract_date_candidates(text)
        
        for candidate in date_candidates:
            try:
                # Parse with dateutil, defaulting to current year
                parsed_date = dateutil_parser.parse(
                    candidate, 
                    default=reference_date.replace(hour=0, minute=0, second=0, microsecond=0),
                    fuzzy=True
                )
                
                # Ensure we're using the current year for dates without explicit year
                if parsed_date.year == reference_date.year or candidate.count('/') <= 1:
                    formatted_date = parsed_date.strftime('%Y-%m-%d')
                    logger.info(f"Parsed with dateutil '{candidate}' → {formatted_date}")
                    return formatted_date
                    
            except (ValueError, TypeError):
                continue
                
        return None
        
    except Exception as e:
        logger.error(f"Error parsing with dateutil from '{text}': {e}")
        return None


def _extract_date_candidates(text: str) -> list:
    """Extract potential date strings from text."""
    candidates = []
    
    # Look for various date patterns
    patterns = [
        r'\b\d{1,2}[/-]\d{1,2}(?:[/-]\d{2,4})?\b',  # MM/DD or MM/DD/YYYY
        r'\b\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\w*\b',  # DD Month
        r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\w*\s+\d{1,2}\b',  # Month DD
        r'\b\d{4}-\d{1,2}-\d{1,2}\b',  # YYYY-MM-DD
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        candidates.extend(matches)
        
    return candidates


def extract_and_parse_date(text: str, reference_date: datetime = None) -> Optional[str]:
    """
    Main function to extract and parse dates from text.
    
    Tries multiple parsing strategies:
    1. Look for existing YYYY-MM-DD format
    2. Parse relative expressions
    3. Parse absolute dates with current year context
    
    Args:
        text: Input text containing date expression
        reference_date: Reference date for calculations (defaults to current date)
        
    Returns:
        Date string in YYYY-MM-DD format or None if no date found
    """
    try:
        if reference_date is None:
            reference_date = datetime.now()
            
        # First, check for existing YYYY-MM-DD format
        date_pattern = r'\d{4}-\d{2}-\d{2}'
        match = re.search(date_pattern, text)
        if match:
            existing_date = match.group()
            logger.debug(f"Found existing formatted date: {existing_date}")
            return existing_date
            
        # Try relative date parsing
        relative_date = parse_relative_date(text, reference_date)
        if relative_date:
            return relative_date
            
        logger.debug(f"No date found in text: '{text}'")
        return None
        
    except Exception as e:
        # Use Task 2.1 error handling
        context = ErrorContext(
            operation="extract_and_parse_date",
            additional_data={
                "input_text": text[:100],
                "reference_date": reference_date.isoformat() if reference_date else None
            }
        )
        log_exception(e, context=context)
        return None


def get_current_date_context() -> Dict[str, Any]:
    """
    Get current date context for LLM prompts.
    
    Returns:
        Dictionary with current_date and current_year for prompt formatting
    """
    now = datetime.now()
    return {
        'current_date': now.strftime('%Y-%m-%d'),
        'current_year': now.year
    }
