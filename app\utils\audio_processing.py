"""
Audio Processing Utilities for Voice Message Processing

This module provides comprehensive audio processing capabilities for voice messages,
including format validation, metadata extraction, quality checks, preprocessing,
and format conversion utilities. It integrates seamlessly with existing voice
message processing infrastructure while maintaining 100% backward compatibility.

Task 2.2 Implementation:
- Audio format validation and metadata extraction
- Audio quality checks and preprocessing
- Format conversion utilities (if needed)
- Temporary file management with automatic cleanup
- Audio duration and size validation

Integration Points:
- MediaDownloadResult from twilio_client.py
- TranscriptionResult from speech_to_text.py
- Existing error handling framework from exceptions.py
- Validation patterns from validation.py
"""

import os
import logging
import tempfile
from pathlib import Path
from dataclasses import dataclass
from typing import Optional, Dict, Tuple, Any, Union
from contextlib import contextmanager

# Import existing validation and error handling
from app.utils.validation import (
    SUPPORTED_AUDIO_FORMATS, MAX_AUDIO_FILE_SIZE_MB, is_valid_audio_format
)
from app.utils.exceptions import (
    BaseBookingException, ErrorSeverity, Error<PERSON>ategory, ErrorContext,
    handle_external_api_error, log_exception
)

# Optional pydub import with graceful fallback
try:
    from pydub import AudioSegment
    from pydub.exceptions import CouldntDecodeError
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    AudioSegment = None
    CouldntDecodeError = Exception

logger = logging.getLogger(__name__)

# Audio processing constants
DEFAULT_SAMPLE_RATE = 16000  # Optimal for speech recognition
DEFAULT_CHANNELS = 1  # Mono for speech processing
MIN_DURATION_SECONDS = 0.1  # Minimum audio duration
MAX_DURATION_SECONDS = 300  # Maximum 5 minutes for voice messages
MIN_QUALITY_BITRATE = 8000  # Minimum bitrate for acceptable quality
OPTIMAL_SAMPLE_RATES = [8000, 16000, 22050, 44100, 48000]  # Common sample rates

@dataclass
class AudioMetadata:
    """Comprehensive audio metadata information."""
    duration_seconds: float = 0.0
    sample_rate: int = 0
    channels: int = 0
    bitrate: Optional[int] = None
    format: Optional[str] = None
    file_size_bytes: int = 0
    is_valid: bool = False
    quality_score: float = 0.0  # 0.0 to 1.0 quality rating
    preprocessing_needed: bool = False
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary for logging/storage."""
        return {
            'duration_seconds': self.duration_seconds,
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'bitrate': self.bitrate,
            'format': self.format,
            'file_size_bytes': self.file_size_bytes,
            'is_valid': self.is_valid,
            'quality_score': self.quality_score,
            'preprocessing_needed': self.preprocessing_needed,
            'error_message': self.error_message
        }

@dataclass
class AudioProcessingResult:
    """Result of audio processing operations."""
    success: bool = False
    processed_file_path: Optional[str] = None
    original_metadata: Optional[AudioMetadata] = None
    processed_metadata: Optional[AudioMetadata] = None
    processing_applied: Optional[str] = None  # Description of processing done
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for logging/storage."""
        return {
            'success': self.success,
            'processed_file_path': self.processed_file_path,
            'original_metadata': self.original_metadata.to_dict() if self.original_metadata else None,
            'processed_metadata': self.processed_metadata.to_dict() if self.processed_metadata else None,
            'processing_applied': self.processing_applied,
            'error_message': self.error_message
        }

class AudioProcessingException(BaseBookingException):
    """Exception for audio processing errors."""
    
    def __init__(
        self,
        message: str,
        audio_file: Optional[str] = None,
        operation: Optional[str] = None,
        original_exception: Optional[Exception] = None,
        user_message: Optional[str] = None
    ):
        context = ErrorContext(
            operation=operation or "audio_processing",
            additional_data={
                "audio_file": Path(audio_file).name if audio_file else "unknown",
                "pydub_available": PYDUB_AVAILABLE
            }
        )
        
        super().__init__(
            message=message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.VALIDATION,
            user_message=user_message or "Unable to process audio file. Please try sending as text.",
            context=context,
            original_exception=original_exception
        )

class AudioProcessor:
    """
    Comprehensive audio processing utilities for voice message handling.
    
    Provides audio validation, metadata extraction, quality assessment,
    preprocessing, and format conversion capabilities while maintaining
    integration with existing voice message processing infrastructure.
    """
    
    def __init__(self):
        """Initialize audio processor with dependency checks."""
        self.pydub_available = PYDUB_AVAILABLE
        if not self.pydub_available:
            logger.warning(
                "pydub not available - advanced audio processing features disabled. "
                "Install pydub for full functionality: pip install pydub"
            )
    
    def extract_audio_metadata(self, file_path: str, content_type: str) -> AudioMetadata:
        """
        Extract comprehensive metadata from audio file.
        
        Args:
            file_path: Path to audio file
            content_type: MIME type of audio file
            
        Returns:
            AudioMetadata with extracted information
        """
        metadata = AudioMetadata()
        
        try:
            # Basic file validation
            path = Path(file_path)
            if not path.exists():
                metadata.error_message = f"Audio file not found: {path.name}"
                return metadata
            
            if not path.is_file():
                metadata.error_message = f"Path is not a file: {path.name}"
                return metadata
            
            # Get file size
            metadata.file_size_bytes = path.stat().st_size
            metadata.format = content_type
            
            # Validate file size
            max_size_bytes = MAX_AUDIO_FILE_SIZE_MB * 1024 * 1024
            if metadata.file_size_bytes > max_size_bytes:
                metadata.error_message = f"File too large: {metadata.file_size_bytes} bytes (max: {max_size_bytes})"
                return metadata
            
            if metadata.file_size_bytes == 0:
                metadata.error_message = "Audio file is empty"
                return metadata
            
            # Validate content type
            if not is_valid_audio_format(content_type):
                metadata.error_message = f"Unsupported audio format: {content_type}"
                return metadata
            
            # Extract detailed metadata using pydub if available
            if self.pydub_available:
                metadata = self._extract_detailed_metadata(file_path, content_type, metadata)
            else:
                # Basic metadata without pydub
                metadata.is_valid = True
                metadata.quality_score = 0.5  # Neutral score without detailed analysis
                logger.info(f"Basic metadata extracted for {path.name} (pydub not available)")
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting audio metadata: {e}")
            metadata.error_message = f"Metadata extraction failed: {str(e)}"
            return metadata
    
    def _extract_detailed_metadata(self, file_path: str, content_type: str, metadata: AudioMetadata) -> AudioMetadata:
        """Extract detailed metadata using pydub (internal method)."""
        try:
            # Load audio file with pydub
            audio = AudioSegment.from_file(file_path)
            
            # Extract basic properties
            metadata.duration_seconds = len(audio) / 1000.0  # Convert ms to seconds
            metadata.sample_rate = audio.frame_rate
            metadata.channels = audio.channels
            
            # Calculate bitrate if possible
            if metadata.duration_seconds > 0:
                # Estimate bitrate from file size and duration
                bits_per_second = (metadata.file_size_bytes * 8) / metadata.duration_seconds
                metadata.bitrate = int(bits_per_second)
            
            # Validate duration
            if metadata.duration_seconds < MIN_DURATION_SECONDS:
                metadata.error_message = f"Audio too short: {metadata.duration_seconds:.2f}s (min: {MIN_DURATION_SECONDS}s)"
                return metadata
            
            if metadata.duration_seconds > MAX_DURATION_SECONDS:
                metadata.error_message = f"Audio too long: {metadata.duration_seconds:.2f}s (max: {MAX_DURATION_SECONDS}s)"
                return metadata
            
            # Calculate quality score and preprocessing needs
            metadata.quality_score = self._calculate_quality_score(metadata)
            metadata.preprocessing_needed = self._needs_preprocessing(metadata)
            metadata.is_valid = True
            
            logger.info(
                f"Detailed metadata extracted: {metadata.duration_seconds:.2f}s, "
                f"{metadata.sample_rate}Hz, {metadata.channels}ch, quality: {metadata.quality_score:.2f}"
            )
            
            return metadata
            
        except CouldntDecodeError as e:
            metadata.error_message = f"Could not decode audio file: {str(e)}"
            return metadata
        except Exception as e:
            metadata.error_message = f"Detailed metadata extraction failed: {str(e)}"
            return metadata

    def _calculate_quality_score(self, metadata: AudioMetadata) -> float:
        """
        Calculate audio quality score for transcription suitability.

        Args:
            metadata: AudioMetadata with basic properties

        Returns:
            Quality score from 0.0 (poor) to 1.0 (excellent)
        """
        score = 0.0

        # Sample rate scoring (higher is generally better for speech)
        if metadata.sample_rate >= 16000:
            score += 0.4
        elif metadata.sample_rate >= 8000:
            score += 0.2

        # Channel scoring (mono is preferred for speech)
        if metadata.channels == 1:
            score += 0.2
        elif metadata.channels == 2:
            score += 0.1

        # Bitrate scoring (if available)
        if metadata.bitrate:
            if metadata.bitrate >= MIN_QUALITY_BITRATE:
                score += 0.2
            else:
                score += 0.1
        else:
            score += 0.1  # Neutral score if bitrate unknown

        # Duration scoring (reasonable length gets bonus)
        if 1.0 <= metadata.duration_seconds <= 60.0:
            score += 0.2
        elif metadata.duration_seconds <= 120.0:
            score += 0.1

        return min(score, 1.0)

    def _needs_preprocessing(self, metadata: AudioMetadata) -> bool:
        """
        Determine if audio needs preprocessing for better transcription.

        Args:
            metadata: AudioMetadata with properties

        Returns:
            True if preprocessing recommended
        """
        # Check if sample rate is not optimal
        if metadata.sample_rate not in OPTIMAL_SAMPLE_RATES:
            return True

        # Check if stereo (should convert to mono)
        if metadata.channels > 1:
            return True

        # Check if quality score is low
        if metadata.quality_score < 0.6:
            return True

        return False

    def validate_audio_quality(self, file_path: str, content_type: str) -> Tuple[bool, str, AudioMetadata]:
        """
        Validate audio quality for transcription suitability.

        Args:
            file_path: Path to audio file
            content_type: MIME type of audio file

        Returns:
            Tuple of (is_suitable, message, metadata)
        """
        try:
            # Extract metadata first
            metadata = self.extract_audio_metadata(file_path, content_type)

            if not metadata.is_valid:
                return False, metadata.error_message or "Invalid audio file", metadata

            # Check quality score
            if metadata.quality_score < 0.3:
                return False, f"Audio quality too low for transcription (score: {metadata.quality_score:.2f})", metadata

            # Check duration limits
            if metadata.duration_seconds < MIN_DURATION_SECONDS:
                return False, f"Audio too short: {metadata.duration_seconds:.2f}s", metadata

            if metadata.duration_seconds > MAX_DURATION_SECONDS:
                return False, f"Audio too long: {metadata.duration_seconds:.2f}s", metadata

            # Quality is acceptable
            quality_level = "excellent" if metadata.quality_score >= 0.8 else \
                           "good" if metadata.quality_score >= 0.6 else "acceptable"

            message = f"Audio quality {quality_level} (score: {metadata.quality_score:.2f})"
            if metadata.preprocessing_needed:
                message += " - preprocessing recommended"

            return True, message, metadata

        except Exception as e:
            error_msg = f"Quality validation failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg, AudioMetadata(error_message=error_msg)

    def preprocess_for_transcription(self, file_path: str, content_type: str) -> AudioProcessingResult:
        """
        Preprocess audio file for optimal transcription accuracy.

        Args:
            file_path: Path to original audio file
            content_type: MIME type of audio file

        Returns:
            AudioProcessingResult with processed file information
        """
        result = AudioProcessingResult()

        try:
            # Extract original metadata
            original_metadata = self.extract_audio_metadata(file_path, content_type)
            result.original_metadata = original_metadata

            if not original_metadata.is_valid:
                result.error_message = f"Cannot preprocess invalid audio: {original_metadata.error_message}"
                return result

            # Check if preprocessing is needed
            if not original_metadata.preprocessing_needed:
                # No preprocessing needed, return original file
                result.success = True
                result.processed_file_path = file_path
                result.processed_metadata = original_metadata
                result.processing_applied = "No preprocessing needed"
                return result

            # Perform preprocessing if pydub is available
            if not self.pydub_available:
                # Fallback: return original file with warning
                result.success = True
                result.processed_file_path = file_path
                result.processed_metadata = original_metadata
                result.processing_applied = "Preprocessing skipped (pydub not available)"
                logger.warning("Audio preprocessing requested but pydub not available")
                return result

            # Perform actual preprocessing
            result = self._perform_preprocessing(file_path, content_type, original_metadata)
            return result

        except Exception as e:
            error_msg = f"Audio preprocessing failed: {str(e)}"
            logger.error(error_msg)
            result.error_message = error_msg
            return result

    def _perform_preprocessing(self, file_path: str, content_type: str, original_metadata: AudioMetadata) -> AudioProcessingResult:
        """Perform actual audio preprocessing (internal method)."""
        result = AudioProcessingResult()
        result.original_metadata = original_metadata

        try:
            # Load audio with pydub
            audio = AudioSegment.from_file(file_path)
            processing_steps = []

            # Convert to mono if stereo
            if audio.channels > 1:
                audio = audio.set_channels(1)
                processing_steps.append("converted to mono")

            # Normalize sample rate if needed
            target_sample_rate = DEFAULT_SAMPLE_RATE
            if audio.frame_rate != target_sample_rate and audio.frame_rate not in OPTIMAL_SAMPLE_RATES:
                audio = audio.set_frame_rate(target_sample_rate)
                processing_steps.append(f"resampled to {target_sample_rate}Hz")

            # Create processed file with secure temporary file handling
            with self._secure_temp_file(suffix=self._get_file_extension(content_type)) as (temp_path, temp_file):
                # Export processed audio
                audio.export(temp_path, format=self._get_export_format(content_type))

                # Extract metadata from processed file
                processed_metadata = self.extract_audio_metadata(temp_path, content_type)

                # Success
                result.success = True
                result.processed_file_path = temp_path
                result.processed_metadata = processed_metadata
                result.processing_applied = f"Applied: {', '.join(processing_steps)}"

                logger.info(f"Audio preprocessing completed: {result.processing_applied}")
                return result

        except Exception as e:
            error_msg = f"Preprocessing operation failed: {str(e)}"
            logger.error(error_msg)
            result.error_message = error_msg
            return result

    @contextmanager
    def _secure_temp_file(self, suffix: str = ".tmp", prefix: str = "audio_proc_"):
        """
        Create a secure temporary file with automatic cleanup.
        Reuses the same pattern as twilio_client.py for consistency.

        Args:
            suffix: File suffix/extension
            prefix: File prefix for identification

        Yields:
            Tuple of (file_path, file_handle)
        """
        temp_file = None
        try:
            # Create temporary file with secure permissions (600 - owner read/write only)
            temp_file = tempfile.NamedTemporaryFile(
                mode='wb',
                suffix=suffix,
                prefix=prefix,
                delete=False  # We'll handle deletion manually for better control
            )

            # Set secure permissions (owner read/write only)
            os.chmod(temp_file.name, 0o600)

            yield temp_file.name, temp_file

        finally:
            # Ensure cleanup happens even if an exception occurs
            if temp_file:
                try:
                    temp_file.close()
                    if os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
                        logger.debug(f"Cleaned up temporary file: {temp_file.name}")
                except Exception as cleanup_error:
                    logger.error(f"Failed to cleanup temporary file {temp_file.name}: {cleanup_error}")

    def _get_file_extension(self, content_type: str) -> str:
        """Get appropriate file extension for content type."""
        extension_map = {
            'audio/ogg': '.ogg',
            'audio/mpeg': '.mp3',
            'audio/mp3': '.mp3',
            'audio/mp4': '.mp4',
            'audio/3gpp': '.3gp',
            'audio/amr': '.amr',
            'audio/amr-nb': '.amr',
            'audio/webm': '.webm'
        }
        return extension_map.get(content_type.lower(), '.audio')

    def _get_export_format(self, content_type: str) -> str:
        """Get pydub export format for content type."""
        format_map = {
            'audio/ogg': 'ogg',
            'audio/mpeg': 'mp3',
            'audio/mp3': 'mp3',
            'audio/mp4': 'mp4',
            'audio/3gpp': '3gp',
            'audio/amr': 'amr',
            'audio/amr-nb': 'amr',
            'audio/webm': 'webm'
        }
        return format_map.get(content_type.lower(), 'wav')  # Default to WAV

    def convert_audio_format(self, file_path: str, source_format: str, target_format: str) -> AudioProcessingResult:
        """
        Convert audio file from one format to another.

        Args:
            file_path: Path to source audio file
            source_format: Source MIME type
            target_format: Target MIME type

        Returns:
            AudioProcessingResult with converted file information
        """
        result = AudioProcessingResult()

        try:
            # Validate inputs
            if not is_valid_audio_format(source_format):
                result.error_message = f"Unsupported source format: {source_format}"
                return result

            if not is_valid_audio_format(target_format):
                result.error_message = f"Unsupported target format: {target_format}"
                return result

            # Check if conversion is needed
            if source_format.lower() == target_format.lower():
                # No conversion needed
                original_metadata = self.extract_audio_metadata(file_path, source_format)
                result.success = True
                result.processed_file_path = file_path
                result.original_metadata = original_metadata
                result.processed_metadata = original_metadata
                result.processing_applied = "No conversion needed (same format)"
                return result

            # Perform conversion if pydub is available
            if not self.pydub_available:
                result.error_message = "Format conversion requires pydub (not available)"
                return result

            # Extract original metadata
            original_metadata = self.extract_audio_metadata(file_path, source_format)
            result.original_metadata = original_metadata

            if not original_metadata.is_valid:
                result.error_message = f"Cannot convert invalid audio: {original_metadata.error_message}"
                return result

            # Perform conversion
            audio = AudioSegment.from_file(file_path)

            with self._secure_temp_file(suffix=self._get_file_extension(target_format)) as (temp_path, temp_file):
                # Export in target format
                export_format = self._get_export_format(target_format)
                audio.export(temp_path, format=export_format)

                # Extract metadata from converted file
                processed_metadata = self.extract_audio_metadata(temp_path, target_format)

                # Success
                result.success = True
                result.processed_file_path = temp_path
                result.processed_metadata = processed_metadata
                result.processing_applied = f"Converted from {source_format} to {target_format}"

                logger.info(f"Audio format conversion completed: {source_format} -> {target_format}")
                return result

        except Exception as e:
            error_msg = f"Format conversion failed: {str(e)}"
            logger.error(error_msg)
            result.error_message = error_msg
            return result

    def validate_audio_file(self, file_path: str, content_type: str) -> Tuple[bool, str, Optional[AudioMetadata]]:
        """
        Comprehensive audio file validation combining all checks.

        Args:
            file_path: Path to audio file
            content_type: MIME type of audio file

        Returns:
            Tuple of (is_valid, message, metadata)
        """
        try:
            # Extract metadata (includes basic validation)
            metadata = self.extract_audio_metadata(file_path, content_type)

            if not metadata.is_valid:
                return False, metadata.error_message or "Invalid audio file", metadata

            # Perform quality validation
            is_quality_ok, quality_msg, _ = self.validate_audio_quality(file_path, content_type)

            if not is_quality_ok:
                return False, quality_msg, metadata

            # All validations passed
            return True, f"Audio file valid - {quality_msg}", metadata

        except Exception as e:
            error_msg = f"Audio validation failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg, None


# Convenience functions for easy integration with existing code

def process_audio_for_transcription(file_path: str, content_type: str) -> AudioProcessingResult:
    """
    Convenience function to process audio file for optimal transcription.

    This function provides a simple interface for the existing voice message
    processing pipeline to enhance audio quality before transcription.

    Args:
        file_path: Path to audio file
        content_type: MIME type of audio file

    Returns:
        AudioProcessingResult with processed file information
    """
    processor = AudioProcessor()
    return processor.preprocess_for_transcription(file_path, content_type)


def validate_audio_for_transcription(file_path: str, content_type: str) -> Tuple[bool, str, Optional[AudioMetadata]]:
    """
    Convenience function to validate audio file for transcription suitability.

    Args:
        file_path: Path to audio file
        content_type: MIME type of audio file

    Returns:
        Tuple of (is_suitable, message, metadata)
    """
    processor = AudioProcessor()
    return processor.validate_audio_file(file_path, content_type)


def extract_audio_metadata(file_path: str, content_type: str) -> AudioMetadata:
    """
    Convenience function to extract audio metadata.

    Args:
        file_path: Path to audio file
        content_type: MIME type of audio file

    Returns:
        AudioMetadata with extracted information
    """
    processor = AudioProcessor()
    return processor.extract_audio_metadata(file_path, content_type)


def is_pydub_available() -> bool:
    """
    Check if pydub is available for advanced audio processing.

    Returns:
        True if pydub is available, False otherwise
    """
    return PYDUB_AVAILABLE


def get_audio_processing_info() -> Dict[str, Any]:
    """
    Get information about audio processing capabilities.

    Returns:
        Dictionary with capability information
    """
    return {
        'pydub_available': PYDUB_AVAILABLE,
        'supported_formats': SUPPORTED_AUDIO_FORMATS,
        'max_file_size_mb': MAX_AUDIO_FILE_SIZE_MB,
        'default_sample_rate': DEFAULT_SAMPLE_RATE,
        'optimal_sample_rates': OPTIMAL_SAMPLE_RATES,
        'max_duration_seconds': MAX_DURATION_SECONDS,
        'min_duration_seconds': MIN_DURATION_SECONDS
    }
