# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Performance tests for voice message processing.

Tests voice processing latency, throughput, cache performance,
and system behavior under load conditions.
"""

import asyncio
import time
import tempfile
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, patch
from typing import List, Dict, Any

import pytest

from app.utils.speech_to_text import SpeechToTextService, TranscriptionResult
from app.utils.cache import TranscriptionCache
from app.utils.audio_processing import AudioProcessor
from app.agent import BookingAgent


class TestVoiceProcessingLatency:
    """Test cases for voice processing latency requirements."""

    @patch('app.utils.speech_to_text.speech.SpeechClient')
    def test_transcription_latency_requirement(self, mock_client_class):
        """Test that transcription meets latency requirements (<5 seconds)."""
        # Mock Google Cloud Speech client
        mock_client = Mock()
        mock_response = Mock()
        mock_response.results = [
            Mock(alternatives=[Mock(transcript="Test transcription", confidence=0.95)])
        ]
        mock_client.recognize.return_value = mock_response
        mock_client_class.return_value = mock_client
        
        # Create temporary audio file
        with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
            temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'x' * 1000)
            temp_file_path = temp_file.name
        
        try:
            with patch('app.utils.speech_to_text.get_voice_config') as mock_config:
                mock_config.return_value.google_cloud_project_id = 'test-project'
                
                service = SpeechToTextService()
                
                start_time = time.time()
                result = service.transcribe_audio(temp_file_path, 'audio/ogg')
                end_time = time.time()
                
                processing_time = end_time - start_time
                
                assert result.success
                assert processing_time < 5.0, f"Transcription took {processing_time:.2f}s, should be <5s"
                
        finally:
            os.unlink(temp_file_path)

    def test_audio_processing_latency(self):
        """Test that audio processing meets latency requirements (<2 seconds)."""
        processor = AudioProcessor()
        
        # Create temporary audio file
        with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
            temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'x' * 1000)
            temp_file_path = temp_file.name
        
        try:
            start_time = time.time()
            
            # Test validation
            validation_result = processor.validate_audio_format(temp_file_path, 'audio/ogg')
            
            # Test metadata extraction
            metadata_result = processor.extract_metadata(temp_file_path, 'audio/ogg')
            
            # Test preprocessing
            preprocessing_result = processor.preprocess_for_transcription(temp_file_path, 'audio/ogg')
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            assert validation_result.success
            assert metadata_result.success
            assert processing_time < 2.0, f"Audio processing took {processing_time:.2f}s, should be <2s"
            
        finally:
            os.unlink(temp_file_path)

    def test_cache_operation_latency(self):
        """Test that cache operations are fast (<100ms)."""
        cache = TranscriptionCache(ttl_minutes=60, max_entries=100)
        
        # Create temporary audio file
        with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
            temp_file.write(b'test_audio_content')
            temp_file_path = temp_file.name
        
        try:
            transcription_result = {
                'success': True,
                'transcript': 'Test transcription',
                'confidence': 0.95,
                'language_code': 'en-US'
            }
            
            # Test cache set operation
            start_time = time.time()
            cache.set(temp_file_path, 'audio/ogg', transcription_result, 2.5, 'en-US')
            set_time = time.time() - start_time
            
            # Test cache get operation
            start_time = time.time()
            result = cache.get(temp_file_path, 'audio/ogg', 'en-US')
            get_time = time.time() - start_time
            
            assert result is not None
            assert set_time < 0.1, f"Cache set took {set_time:.3f}s, should be <0.1s"
            assert get_time < 0.1, f"Cache get took {get_time:.3f}s, should be <0.1s"
            
        finally:
            os.unlink(temp_file_path)

    def test_agent_processing_latency(self):
        """Test that agent processing meets latency requirements (<3 seconds)."""
        agent = BookingAgent()
        
        voice_metadata = {
            'original_media_url': 'https://test.com/audio.ogg',
            'media_content_type': 'audio/ogg',
            'transcription_confidence': 0.95,
            'audio_duration': 10.0
        }
        
        with patch('app.agent.get_booking_context') as mock_get_context:
            with patch('app.agent.save_booking_context') as mock_save_context:
                mock_get_context.return_value = {
                    'conversation_history': [],
                    'client_name': None,
                    'phone_number': '+1234567890'
                }
                
                start_time = time.time()
                response = agent.process_voice_message(
                    "I need to book a massage appointment for tomorrow at 3 PM",
                    "+1234567890",
                    voice_metadata
                )
                end_time = time.time()
                
                processing_time = end_time - start_time
                
                assert isinstance(response, str)
                assert len(response) > 0
                assert processing_time < 3.0, f"Agent processing took {processing_time:.2f}s, should be <3s"


class TestVoiceProcessingThroughput:
    """Test cases for voice processing throughput and concurrent handling."""

    def test_concurrent_transcription_processing(self):
        """Test concurrent transcription processing capability."""
        def create_mock_service():
            with patch('app.utils.speech_to_text.speech.SpeechClient') as mock_client_class:
                mock_client = Mock()
                mock_response = Mock()
                mock_response.results = [
                    Mock(alternatives=[Mock(transcript="Concurrent test", confidence=0.95)])
                ]
                mock_client.recognize.return_value = mock_response
                mock_client_class.return_value = mock_client
                
                with patch('app.utils.speech_to_text.get_voice_config') as mock_config:
                    mock_config.return_value.google_cloud_project_id = 'test-project'
                    return SpeechToTextService()
        
        def process_audio(service, file_path):
            return service.transcribe_audio(file_path, 'audio/ogg')
        
        # Create multiple temporary audio files
        temp_files = []
        for i in range(5):
            temp_file = tempfile.NamedTemporaryFile(suffix='.ogg', delete=False)
            temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + f'content{i}'.encode() * 100)
            temp_file.close()
            temp_files.append(temp_file.name)
        
        try:
            service = create_mock_service()
            
            start_time = time.time()
            
            # Process files concurrently
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [
                    executor.submit(process_audio, service, file_path)
                    for file_path in temp_files
                ]
                
                results = [future.result() for future in as_completed(futures)]
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # All should succeed
            assert all(result.success for result in results)
            
            # Concurrent processing should be faster than sequential
            assert total_time < 10.0, f"Concurrent processing took {total_time:.2f}s, should be <10s"
            
        finally:
            for file_path in temp_files:
                if os.path.exists(file_path):
                    os.unlink(file_path)

    def test_cache_performance_under_load(self):
        """Test cache performance under high load conditions."""
        cache = TranscriptionCache(ttl_minutes=60, max_entries=1000)
        
        # Create multiple audio files and cache entries
        temp_files = []
        for i in range(50):
            temp_file = tempfile.NamedTemporaryFile(suffix='.ogg', delete=False)
            temp_file.write(f'audio_content_{i}'.encode() * 20)
            temp_file.close()
            temp_files.append(temp_file.name)
        
        try:
            transcription_results = []
            for i in range(50):
                result = {
                    'success': True,
                    'transcript': f'Transcription result {i}',
                    'confidence': 0.9 + (i % 10) * 0.01,
                    'language_code': 'en-US'
                }
                transcription_results.append(result)
            
            # Measure cache set performance
            start_time = time.time()
            for i, file_path in enumerate(temp_files):
                cache.set(file_path, 'audio/ogg', transcription_results[i], 2.0, 'en-US')
            set_time = time.time() - start_time
            
            # Measure cache get performance
            start_time = time.time()
            hit_count = 0
            for file_path in temp_files:
                result = cache.get(file_path, 'audio/ogg', 'en-US')
                if result is not None:
                    hit_count += 1
            get_time = time.time() - start_time
            
            # Performance assertions
            assert set_time < 5.0, f"Cache set operations took {set_time:.2f}s, should be <5s"
            assert get_time < 2.0, f"Cache get operations took {get_time:.2f}s, should be <2s"
            assert hit_count == 50, f"Expected 50 cache hits, got {hit_count}"
            
            # Check cache statistics
            stats = cache.get_stats()
            assert stats['hit_rate_percent'] == 100.0
            assert stats['cache_size'] == 50
            
        finally:
            for file_path in temp_files:
                if os.path.exists(file_path):
                    os.unlink(file_path)

    def test_memory_usage_under_load(self):
        """Test memory usage remains reasonable under load."""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        cache = TranscriptionCache(ttl_minutes=60, max_entries=100)
        
        # Create many cache entries
        for i in range(200):  # More than max_entries to test cleanup
            temp_file = tempfile.NamedTemporaryFile(suffix='.ogg', delete=False)
            temp_file.write(f'audio_content_{i}'.encode() * 100)
            temp_file.close()
            
            try:
                result = {
                    'success': True,
                    'transcript': f'Long transcription result {i} ' * 50,  # Large transcript
                    'confidence': 0.95,
                    'language_code': 'en-US'
                }
                
                cache.set(temp_file.name, 'audio/ogg', result, 2.0, 'en-US')
                
            finally:
                os.unlink(temp_file.name)
        
        # Force garbage collection
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (<100MB for this test)
        assert memory_increase < 100, f"Memory increased by {memory_increase:.1f}MB, should be <100MB"
        
        # Cache should have been cleaned up to max_entries
        assert len(cache.cache) <= cache.max_entries


class TestVoiceProcessingStressTest:
    """Stress tests for voice processing system."""

    def test_rapid_sequential_requests(self):
        """Test system behavior under rapid sequential requests."""
        def create_mock_components():
            # Mock all components for fast testing
            with patch('app.utils.speech_to_text.speech.SpeechClient') as mock_client_class:
                mock_client = Mock()
                mock_response = Mock()
                mock_response.results = [
                    Mock(alternatives=[Mock(transcript="Stress test", confidence=0.95)])
                ]
                mock_client.recognize.return_value = mock_response
                mock_client_class.return_value = mock_client
                
                with patch('app.utils.speech_to_text.get_voice_config') as mock_config:
                    mock_config.return_value.google_cloud_project_id = 'test-project'
                    
                    service = SpeechToTextService()
                    processor = AudioProcessor()
                    agent = BookingAgent()
                    
                    return service, processor, agent
        
        service, processor, agent = create_mock_components()
        
        # Create temporary audio file
        with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
            temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00' + b'stress_test' * 100)
            temp_file_path = temp_file.name
        
        try:
            start_time = time.time()
            
            # Process 20 requests rapidly
            for i in range(20):
                # Audio processing
                validation_result = processor.validate_audio_format(temp_file_path, 'audio/ogg')
                assert validation_result.success
                
                # Transcription (with cache)
                transcription_result = service.transcribe_audio(temp_file_path, 'audio/ogg')
                assert transcription_result.success
                
                # Agent processing
                with patch('app.agent.get_booking_context') as mock_get_context:
                    with patch('app.agent.save_booking_context'):
                        mock_get_context.return_value = {
                            'conversation_history': [],
                            'client_name': None,
                            'phone_number': f'+123456789{i}'
                        }
                        
                        response = agent.process_voice_message(
                            f"Stress test message {i}",
                            f"+123456789{i}",
                            {'transcription_confidence': 0.95}
                        )
                        assert isinstance(response, str)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Should handle 20 requests in reasonable time
            assert total_time < 30.0, f"20 requests took {total_time:.2f}s, should be <30s"
            
        finally:
            os.unlink(temp_file_path)

    def test_error_recovery_under_load(self):
        """Test system error recovery under load conditions."""
        cache = TranscriptionCache(ttl_minutes=60, max_entries=50)
        
        # Simulate mixed success/failure scenarios
        success_count = 0
        error_count = 0
        
        for i in range(100):
            try:
                if i % 3 == 0:  # Simulate 33% failure rate
                    raise Exception(f"Simulated error {i}")
                
                # Successful operation
                temp_file = tempfile.NamedTemporaryFile(suffix='.ogg', delete=False)
                temp_file.write(f'content_{i}'.encode())
                temp_file.close()
                
                try:
                    result = {
                        'success': True,
                        'transcript': f'Result {i}',
                        'confidence': 0.95,
                        'language_code': 'en-US'
                    }
                    
                    cache.set(temp_file.name, 'audio/ogg', result, 2.0, 'en-US')
                    cached = cache.get(temp_file.name, 'audio/ogg', 'en-US')
                    
                    if cached is not None:
                        success_count += 1
                        
                finally:
                    os.unlink(temp_file.name)
                    
            except Exception:
                error_count += 1
                # System should continue operating despite errors
                continue
        
        # Verify system continued operating
        assert success_count > 0, "No successful operations completed"
        assert error_count > 0, "No errors were simulated"
        
        # Cache should still be functional
        stats = cache.get_stats()
        assert stats['hits'] > 0 or stats['misses'] > 0
