# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Load tests for voice message processing using Locust.

Tests concurrent voice message handling, system throughput,
and performance under realistic load conditions.
"""

import json
import logging
import os
import random
import time
from typing import Dict, Any

from locust import HttpUser, between, task, events

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class VoiceMessageUser(HttpUser):
    """
    Simulates a user sending voice messages to the WhatsApp webhook.
    
    This load test simulates realistic voice message scenarios including:
    - Different audio formats and sizes
    - Various transcription complexities
    - Mixed success and error scenarios
    - Realistic user behavior patterns
    """
    
    wait_time = between(5, 15)  # Wait 5-15 seconds between voice messages (realistic)
    
    def on_start(self):
        """Initialize user session."""
        self.phone_number = f"+1555{random.randint(1000000, 9999999)}"
        self.message_count = 0
        logger.info(f"Voice message user started: {self.phone_number}")
    
    @task(70)
    def send_voice_message_booking_request(self):
        """Send voice message with booking request (70% of messages)."""
        self.message_count += 1
        
        # Simulate different booking scenarios
        booking_scenarios = [
            "I need to book a massage appointment for tomorrow at 3 PM",
            "Can I schedule a facial for next Tuesday morning?",
            "I'd like to book a cleaning service for this weekend",
            "Do you have availability for a waxing appointment on Friday?",
            "I want to reschedule my appointment to next week",
        ]
        
        # Simulate voice message with realistic audio metadata
        audio_scenarios = [
            {"format": "audio/ogg", "size": "medium", "quality": "good"},
            {"format": "audio/mpeg", "size": "large", "quality": "excellent"},
            {"format": "audio/mp4", "size": "small", "quality": "fair"},
            {"format": "audio/3gpp", "size": "medium", "quality": "good"},
        ]
        
        scenario = random.choice(booking_scenarios)
        audio = random.choice(audio_scenarios)
        
        self._send_voice_message(scenario, audio, "booking_request")
    
    @task(20)
    def send_voice_message_followup(self):
        """Send voice message with follow-up questions (20% of messages)."""
        self.message_count += 1
        
        followup_scenarios = [
            "What time slots do you have available?",
            "How much does the service cost?",
            "Can you confirm my appointment details?",
            "I need to cancel my appointment",
            "Is there a waiting list for earlier slots?",
        ]
        
        scenario = random.choice(followup_scenarios)
        audio = {"format": "audio/ogg", "size": "small", "quality": "good"}
        
        self._send_voice_message(scenario, audio, "followup")
    
    @task(10)
    def send_voice_message_complex(self):
        """Send complex voice message with multiple requests (10% of messages)."""
        self.message_count += 1
        
        complex_scenarios = [
            "Hi, I need to book a massage for tomorrow at 3 PM, but I also want to know if you have any discounts for regular customers, and can you tell me what other services you offer?",
            "I'm calling to reschedule my Friday appointment to next week, preferably Tuesday or Wednesday morning, and I also want to add a facial to my booking if possible",
            "Can you help me book three different appointments - a cleaning service for this weekend, a massage for next Tuesday, and a facial for the following Friday?",
        ]
        
        scenario = random.choice(complex_scenarios)
        audio = {"format": "audio/ogg", "size": "large", "quality": "excellent"}
        
        self._send_voice_message(scenario, audio, "complex")
    
    def _send_voice_message(self, scenario: str, audio: Dict[str, str], message_type: str):
        """Send voice message to webhook endpoint."""
        # Simulate realistic audio file URLs
        audio_urls = [
            "https://api.twilio.com/2010-04-01/Accounts/test/Recordings/voice1.ogg",
            "https://api.twilio.com/2010-04-01/Accounts/test/Recordings/voice2.mp3",
            "https://api.twilio.com/2010-04-01/Accounts/test/Recordings/voice3.mp4",
        ]
        
        # Prepare webhook data (Twilio format)
        webhook_data = {
            "From": f"whatsapp:{self.phone_number}",
            "Body": "",  # Empty for voice messages
            "NumMedia": "1",
            "MediaUrl0": random.choice(audio_urls),
            "MediaContentType0": audio["format"],
            "MessageSid": f"SM{random.randint(10**31, 10**32-1):032x}",
            "AccountSid": f"AC{random.randint(10**31, 10**32-1):032x}",
        }
        
        start_time = time.time()
        
        with self.client.post(
            "/webhook",
            data=webhook_data,
            catch_response=True,
            name=f"/webhook voice_{message_type}",
        ) as response:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if response.status_code == 200:
                # Check response content for success indicators
                response_text = response.text.lower()
                
                if "error" in response_text or "unable" in response_text:
                    response.failure(f"Voice processing failed: {response_text[:100]}")
                else:
                    # Log performance metrics
                    self._log_performance_metrics(message_type, response_time, audio)
                    
                    # Verify response time meets requirements
                    if response_time > 10000:  # 10 seconds
                        response.failure(f"Response time {response_time:.0f}ms exceeds 10s limit")
                    elif response_time > 7000:  # 7 seconds warning
                        logger.warning(f"Slow response: {response_time:.0f}ms for {message_type}")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text[:100]}")
    
    def _log_performance_metrics(self, message_type: str, response_time: float, audio: Dict[str, str]):
        """Log detailed performance metrics."""
        metrics = {
            "user": self.phone_number,
            "message_count": self.message_count,
            "message_type": message_type,
            "response_time_ms": response_time,
            "audio_format": audio["format"],
            "audio_size": audio["size"],
            "audio_quality": audio["quality"],
            "timestamp": time.time()
        }
        
        # Log to console for monitoring
        logger.info(f"Voice message processed: {json.dumps(metrics)}")


class VoiceProcessingStressUser(HttpUser):
    """
    High-frequency user for stress testing voice processing system.
    
    Simulates aggressive usage patterns to test system limits.
    """
    
    wait_time = between(1, 3)  # Aggressive timing for stress testing
    
    def on_start(self):
        """Initialize stress test user."""
        self.phone_number = f"+1999{random.randint(1000000, 9999999)}"
        self.stress_message_count = 0
        logger.info(f"Stress test user started: {self.phone_number}")
    
    @task
    def rapid_voice_messages(self):
        """Send rapid succession of voice messages."""
        self.stress_message_count += 1
        
        # Short, simple messages for rapid processing
        quick_messages = [
            "Book appointment",
            "Cancel booking",
            "Check availability",
            "Confirm time",
            "Reschedule please",
        ]
        
        message = random.choice(quick_messages)
        
        webhook_data = {
            "From": f"whatsapp:{self.phone_number}",
            "Body": "",
            "NumMedia": "1",
            "MediaUrl0": "https://api.twilio.com/test/quick-voice.ogg",
            "MediaContentType0": "audio/ogg",
            "MessageSid": f"SM{random.randint(10**31, 10**32-1):032x}",
        }
        
        with self.client.post(
            "/webhook",
            data=webhook_data,
            catch_response=True,
            name="/webhook stress_test",
        ) as response:
            if response.status_code != 200:
                response.failure(f"Stress test failed: HTTP {response.status_code}")
            elif self.stress_message_count % 10 == 0:
                logger.info(f"Stress user {self.phone_number}: {self.stress_message_count} messages sent")


# Locust event handlers for custom metrics
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, response, context, exception, **kwargs):
    """Handle request events for custom metrics."""
    if name.startswith("/webhook voice_"):
        # Track voice-specific metrics
        message_type = name.split("_")[-1]
        
        # Log performance thresholds
        if response_time > 10000:  # 10 seconds
            logger.error(f"PERFORMANCE ALERT: {message_type} took {response_time:.0f}ms")
        elif response_time > 7000:  # 7 seconds
            logger.warning(f"PERFORMANCE WARNING: {message_type} took {response_time:.0f}ms")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Handle test start event."""
    logger.info("Voice processing load test started")
    logger.info(f"Target host: {environment.host}")
    logger.info("Performance thresholds:")
    logger.info("  - Target: <5s per voice message")
    logger.info("  - Warning: >7s per voice message")
    logger.info("  - Critical: >10s per voice message")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Handle test stop event."""
    logger.info("Voice processing load test completed")
    
    # Log summary statistics
    stats = environment.stats
    total_requests = stats.total.num_requests
    total_failures = stats.total.num_failures
    avg_response_time = stats.total.avg_response_time
    
    logger.info(f"Total requests: {total_requests}")
    logger.info(f"Total failures: {total_failures}")
    logger.info(f"Failure rate: {(total_failures/total_requests*100):.1f}%")
    logger.info(f"Average response time: {avg_response_time:.0f}ms")
    
    # Performance assessment
    if avg_response_time > 10000:
        logger.error("PERFORMANCE CRITICAL: Average response time exceeds 10s")
    elif avg_response_time > 7000:
        logger.warning("PERFORMANCE WARNING: Average response time exceeds 7s")
    elif avg_response_time < 5000:
        logger.info("PERFORMANCE EXCELLENT: Average response time under 5s")
    else:
        logger.info("PERFORMANCE GOOD: Average response time acceptable")


# Custom user classes for different load scenarios
class LightLoadUser(VoiceMessageUser):
    """Light load user for baseline testing."""
    weight = 1
    wait_time = between(10, 30)  # Slower pace


class MediumLoadUser(VoiceMessageUser):
    """Medium load user for normal usage simulation."""
    weight = 3
    wait_time = between(5, 15)  # Normal pace


class HeavyLoadUser(VoiceMessageUser):
    """Heavy load user for peak usage simulation."""
    weight = 2
    wait_time = between(2, 8)  # Faster pace


# Usage instructions:
"""
To run voice processing load tests:

1. Basic load test (10 users, 2 minutes):
   locust -f voice_processing_load_test.py --host=http://localhost:8000 -u 10 -r 2 -t 2m

2. Stress test (50 users, 5 minutes):
   locust -f voice_processing_load_test.py --host=http://localhost:8000 -u 50 -r 5 -t 5m

3. Peak load test (100 users, 10 minutes):
   locust -f voice_processing_load_test.py --host=http://localhost:8000 -u 100 -r 10 -t 10m

4. Web UI mode (for interactive monitoring):
   locust -f voice_processing_load_test.py --host=http://localhost:8000

Performance targets:
- Response time: <5s (target), <10s (maximum)
- Throughput: >10 voice messages/second
- Error rate: <1%
- System stability: No crashes or memory leaks
"""
