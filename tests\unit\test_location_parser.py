"""
Unit tests for location parsing utilities (Task 7.1).

Tests the core location parsing functionality for WhatsApp location sharing,
including native WhatsApp location data and Google Maps URL parsing.
"""

import pytest
from unittest.mock import Mock
from app.utils.location_parser import (
    extract_location_data,
    parse_google_maps_url,
    format_location_for_odoo,
    _validate_coordinates,
    _extract_coordinates_from_url,
    _parse_coordinate_string,
    _format_location_display
)


class TestExtractLocationData:
    """Test the main extract_location_data function."""

    def test_native_whatsapp_location_complete(self):
        """Test extraction from native WhatsApp location with all fields."""
        webhook_input = Mock()
        webhook_input.Latitude = 40.7128
        webhook_input.Longitude = -74.0060
        webhook_input.Address = "New York, NY"
        webhook_input.Label = "Empire State Building"
        webhook_input.Body = ""

        result = extract_location_data(webhook_input)

        assert result is not None
        assert result['type'] == 'native_whatsapp'
        assert result['latitude'] == 40.7128
        assert result['longitude'] == -74.0060
        assert result['address'] == "New York, NY"
        assert result['label'] == "Empire State Building"
        assert result['formatted_location'] == "40.7128,-74.0060"
        assert "Empire State Building" in result['display_text']
        assert "New York, NY" in result['display_text']
        assert "📍 40.712800, -74.006000" in result['display_text']

    def test_native_whatsapp_location_minimal(self):
        """Test extraction from native WhatsApp location with only coordinates."""
        webhook_input = Mock()
        webhook_input.Latitude = 51.5074
        webhook_input.Longitude = -0.1278
        webhook_input.Address = ""
        webhook_input.Label = ""
        webhook_input.Body = ""

        result = extract_location_data(webhook_input)

        assert result is not None
        assert result['type'] == 'native_whatsapp'
        assert result['latitude'] == 51.5074
        assert result['longitude'] == -0.1278
        assert result['address'] == ""
        assert result['label'] == ""
        assert result['formatted_location'] == "51.5074,-0.1278"
        assert result['display_text'] == "📍 51.507400, -0.127800"

    def test_google_maps_url_extraction(self):
        """Test extraction from Google Maps URL in message body."""
        webhook_input = Mock()
        webhook_input.Latitude = None
        webhook_input.Longitude = None
        webhook_input.Body = "Check this out: https://maps.google.com/?q=40.7128,-74.0060"

        result = extract_location_data(webhook_input)

        assert result is not None
        assert result['type'] == 'google_maps_url'
        assert result['latitude'] == 40.7128
        assert result['longitude'] == -74.0060
        assert result['formatted_location'] == "40.7128,-74.0060"
        assert result['original_url'] == "https://maps.google.com/?q=40.7128,-74.0060"

    def test_invalid_coordinates(self):
        """Test handling of invalid coordinates."""
        webhook_input = Mock()
        webhook_input.Latitude = 91.0  # Invalid latitude
        webhook_input.Longitude = -74.0060
        webhook_input.Address = ""
        webhook_input.Label = ""
        webhook_input.Body = ""

        result = extract_location_data(webhook_input)

        assert result is None

    def test_no_location_data(self):
        """Test when no location data is present."""
        webhook_input = Mock()
        webhook_input.Latitude = None
        webhook_input.Longitude = None
        webhook_input.Body = "Just a regular text message"

        result = extract_location_data(webhook_input)

        assert result is None

    def test_exception_handling(self):
        """Test graceful handling of exceptions."""
        webhook_input = Mock()
        webhook_input.Latitude = "invalid"  # This should cause an exception
        webhook_input.Longitude = -74.0060

        result = extract_location_data(webhook_input)

        assert result is None


class TestParseGoogleMapsUrl:
    """Test Google Maps URL parsing functionality."""

    def test_standard_google_maps_url(self):
        """Test parsing standard Google Maps URL."""
        text = "Check this location: https://maps.google.com/?q=40.7128,-74.0060"
        
        result = parse_google_maps_url(text)

        assert result is not None
        assert result['latitude'] == 40.7128
        assert result['longitude'] == -74.0060
        assert result['original_url'] == "https://maps.google.com/?q=40.7128,-74.0060"

    def test_google_maps_with_path(self):
        """Test parsing Google Maps URL with /maps path."""
        text = "Location: https://maps.google.com/maps?q=51.5074,-0.1278"
        
        result = parse_google_maps_url(text)

        assert result is not None
        assert result['latitude'] == 51.5074
        assert result['longitude'] == -0.1278

    def test_shortened_google_maps_url(self):
        """Test parsing shortened Google Maps URL."""
        text = "https://goo.gl/maps/xyz123?q=40.7128,-74.0060"
        
        result = parse_google_maps_url(text)

        assert result is not None
        assert result['latitude'] == 40.7128
        assert result['longitude'] == -74.0060

    def test_new_shortened_format(self):
        """Test parsing new shortened Google Maps format."""
        text = "https://maps.app.goo.gl/xyz123?q=48.8566,2.3522"
        
        result = parse_google_maps_url(text)

        assert result is not None
        assert result['latitude'] == 48.8566
        assert result['longitude'] == 2.3522

    def test_no_coordinates_in_url(self):
        """Test URL without extractable coordinates."""
        text = "https://maps.google.com/search/restaurants"
        
        result = parse_google_maps_url(text)

        assert result is None

    def test_invalid_coordinates_in_url(self):
        """Test URL with invalid coordinates."""
        text = "https://maps.google.com/?q=91.0,-181.0"  # Invalid coordinates
        
        result = parse_google_maps_url(text)

        assert result is None

    def test_no_url_in_text(self):
        """Test text without any URLs."""
        text = "Just a regular message without any URLs"
        
        result = parse_google_maps_url(text)

        assert result is None

    def test_empty_text(self):
        """Test empty or None text."""
        assert parse_google_maps_url("") is None
        assert parse_google_maps_url(None) is None


class TestFormatLocationForOdoo:
    """Test Odoo formatting functionality."""

    def test_valid_location_data(self):
        """Test formatting valid location data for Odoo."""
        location_data = {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'address': 'New York, NY',
            'label': 'Empire State Building'
        }
        
        result = format_location_for_odoo(location_data)

        assert result == "40.7128,-74.0060"

    def test_missing_coordinates(self):
        """Test handling of missing coordinates."""
        location_data = {
            'address': 'New York, NY',
            'label': 'Empire State Building'
        }
        
        result = format_location_for_odoo(location_data)

        assert result == ""

    def test_invalid_coordinates(self):
        """Test handling of invalid coordinates."""
        location_data = {
            'latitude': 91.0,  # Invalid
            'longitude': -74.0060,
        }
        
        result = format_location_for_odoo(location_data)

        assert result == ""

    def test_none_input(self):
        """Test handling of None input."""
        result = format_location_for_odoo(None)

        assert result == ""

    def test_empty_dict(self):
        """Test handling of empty dictionary."""
        result = format_location_for_odoo({})

        assert result == ""


class TestValidateCoordinates:
    """Test coordinate validation functionality."""

    def test_valid_coordinates(self):
        """Test valid coordinate ranges."""
        assert _validate_coordinates(40.7128, -74.0060) is True
        assert _validate_coordinates(0, 0) is True
        assert _validate_coordinates(90, 180) is True
        assert _validate_coordinates(-90, -180) is True

    def test_invalid_latitude(self):
        """Test invalid latitude values."""
        assert _validate_coordinates(91, 0) is False
        assert _validate_coordinates(-91, 0) is False

    def test_invalid_longitude(self):
        """Test invalid longitude values."""
        assert _validate_coordinates(0, 181) is False
        assert _validate_coordinates(0, -181) is False

    def test_non_numeric_values(self):
        """Test non-numeric coordinate values."""
        assert _validate_coordinates("40.7128", -74.0060) is False
        assert _validate_coordinates(40.7128, "invalid") is False
        assert _validate_coordinates(None, -74.0060) is False


class TestParseCoordinateString:
    """Test coordinate string parsing."""

    def test_valid_coordinate_string(self):
        """Test parsing valid coordinate strings."""
        result = _parse_coordinate_string("40.7128,-74.0060")
        assert result == (40.7128, -74.0060)

        result = _parse_coordinate_string("51.5074,-0.1278")
        assert result == (51.5074, -0.1278)

    def test_coordinate_string_with_spaces(self):
        """Test parsing coordinate strings with spaces."""
        result = _parse_coordinate_string("40.7128, -74.0060")
        assert result == (40.7128, -74.0060)

    def test_invalid_coordinate_string(self):
        """Test parsing invalid coordinate strings."""
        assert _parse_coordinate_string("invalid,string") is None
        assert _parse_coordinate_string("40.7128") is None  # Missing longitude
        assert _parse_coordinate_string("91,-181") is None  # Invalid ranges

    def test_empty_coordinate_string(self):
        """Test parsing empty coordinate strings."""
        assert _parse_coordinate_string("") is None
        assert _parse_coordinate_string(None) is None


class TestFormatLocationDisplay:
    """Test location display formatting."""

    def test_complete_location_display(self):
        """Test formatting with all components."""
        result = _format_location_display(
            40.7128, -74.0060, 
            "New York, NY", 
            "Empire State Building"
        )
        
        assert "Empire State Building" in result
        assert "New York, NY" in result
        assert "📍 40.712800, -74.006000" in result

    def test_coordinates_only_display(self):
        """Test formatting with coordinates only."""
        result = _format_location_display(40.7128, -74.0060)
        
        assert result == "📍 40.712800, -74.006000"

    def test_with_label_only(self):
        """Test formatting with label only."""
        result = _format_location_display(
            40.7128, -74.0060, 
            None, 
            "Empire State Building"
        )
        
        assert "Empire State Building" in result
        assert "📍 40.712800, -74.006000" in result

    def test_error_handling_in_display(self):
        """Test error handling in display formatting."""
        # This should not crash even with invalid inputs
        result = _format_location_display("invalid", -74.0060)
        
        assert "📍 Location" in result or "📍" in result
