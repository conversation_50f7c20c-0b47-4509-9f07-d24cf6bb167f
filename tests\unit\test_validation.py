"""
Unit tests for validation utilities (Task 7.2).

Tests the enhanced WebhookInput model with location sharing capabilities
while ensuring backward compatibility with existing functionality.
"""

import pytest
from pydantic import ValidationError
from app.utils.validation import WebhookInput, ValidationError as CustomValidationError


class TestWebhookInputBasicFunctionality:
    """Test existing WebhookInput functionality (regression testing)."""

    def test_valid_text_message(self):
        """Test valid text message (existing functionality)."""
        webhook_input = WebhookInput(
            Body="Hello, I want to book an appointment",
            From="whatsapp:+**********"
        )
        
        assert webhook_input.Body == "Hello, I want to book an appointment"
        assert webhook_input.From == "whatsapp:+**********"
        assert webhook_input.NumMedia == 0
        assert webhook_input.MediaUrl0 is None
        assert webhook_input.MediaContentType0 is None
        # New location fields should default to None
        assert webhook_input.Latitude is None
        assert webhook_input.Longitude is None
        assert webhook_input.Address is None
        assert webhook_input.Label is None

    def test_valid_voice_message(self):
        """Test valid voice message (existing functionality)."""
        webhook_input = WebhookInput(
            Body="",
            From="whatsapp:+**********",
            NumMedia=1,
            MediaUrl0="https://api.twilio.com/2010-04-01/Accounts/AC123/Messages/MM123/Media/ME123",
            MediaContentType0="audio/ogg"
        )
        
        assert webhook_input.is_voice_message() is True
        assert webhook_input.is_location_message() is False

    def test_empty_body_text_message_fails(self):
        """Test that empty body text messages still fail validation."""
        with pytest.raises(ValidationError):
            WebhookInput(
                Body="",
                From="whatsapp:+**********"
            )

    def test_invalid_phone_number_fails(self):
        """Test that invalid phone numbers still fail validation."""
        with pytest.raises(ValidationError):
            WebhookInput(
                Body="Hello",
                From="invalid-phone"
            )


class TestWebhookInputLocationFields:
    """Test new location field functionality."""

    def test_valid_native_location_message(self):
        """Test valid native WhatsApp location message."""
        webhook_input = WebhookInput(
            Body="",  # Empty body is allowed for native location
            From="whatsapp:+**********",
            Latitude=40.7128,
            Longitude=-74.0060,
            Address="New York, NY",
            Label="Empire State Building"
        )
        
        assert webhook_input.Latitude == 40.7128
        assert webhook_input.Longitude == -74.0060
        assert webhook_input.Address == "New York, NY"
        assert webhook_input.Label == "Empire State Building"
        assert webhook_input.is_location_message() is True
        assert webhook_input.is_voice_message() is False

    def test_valid_location_with_text(self):
        """Test location message with accompanying text."""
        webhook_input = WebhookInput(
            Body="Here's my location",
            From="whatsapp:+**********",
            Latitude=51.5074,
            Longitude=-0.1278
        )
        
        assert webhook_input.Body == "Here's my location"
        assert webhook_input.Latitude == 51.5074
        assert webhook_input.Longitude == -0.1278
        assert webhook_input.is_location_message() is True

    def test_google_maps_url_detection(self):
        """Test Google Maps URL detection in message body."""
        webhook_input = WebhookInput(
            Body="Check this out: https://maps.google.com/?q=40.7128,-74.0060",
            From="whatsapp:+**********"
        )
        
        assert webhook_input.is_location_message() is True
        assert webhook_input._contains_google_maps_link() is True

    def test_multiple_google_maps_formats(self):
        """Test detection of various Google Maps URL formats."""
        urls = [
            "https://maps.google.com/?q=40.7128,-74.0060",
            "https://goo.gl/maps/xyz123",
            "https://maps.app.goo.gl/abc456",
            "Check this: https://maps.google.com/maps?q=51.5074,-0.1278"
        ]
        
        for url in urls:
            webhook_input = WebhookInput(
                Body=url,
                From="whatsapp:+**********"
            )
            assert webhook_input._contains_google_maps_link() is True
            assert webhook_input.is_location_message() is True


class TestLocationFieldValidation:
    """Test validation of location fields."""

    def test_invalid_latitude_range(self):
        """Test validation of latitude range."""
        with pytest.raises(ValidationError, match="Latitude must be between -90 and 90"):
            WebhookInput(
                Body="Location",
                From="whatsapp:+**********",
                Latitude=91.0,  # Invalid
                Longitude=-74.0060
            )

        with pytest.raises(ValidationError, match="Latitude must be between -90 and 90"):
            WebhookInput(
                Body="Location",
                From="whatsapp:+**********",
                Latitude=-91.0,  # Invalid
                Longitude=-74.0060
            )

    def test_invalid_longitude_range(self):
        """Test validation of longitude range."""
        with pytest.raises(ValidationError, match="Longitude must be between -180 and 180"):
            WebhookInput(
                Body="Location",
                From="whatsapp:+**********",
                Latitude=40.7128,
                Longitude=181.0  # Invalid
            )

        with pytest.raises(ValidationError, match="Longitude must be between -180 and 180"):
            WebhookInput(
                Body="Location",
                From="whatsapp:+**********",
                Latitude=40.7128,
                Longitude=-181.0  # Invalid
            )

    def test_non_numeric_coordinates(self):
        """Test validation of non-numeric coordinates."""
        with pytest.raises(ValidationError, match="Latitude must be a number"):
            WebhookInput(
                Body="Location",
                From="whatsapp:+**********",
                Latitude="invalid",
                Longitude=-74.0060
            )

        with pytest.raises(ValidationError, match="Longitude must be a number"):
            WebhookInput(
                Body="Location",
                From="whatsapp:+**********",
                Latitude=40.7128,
                Longitude="invalid"
            )

    def test_address_sanitization(self):
        """Test address field sanitization."""
        webhook_input = WebhookInput(
            Body="Location",
            From="whatsapp:+**********",
            Latitude=40.7128,
            Longitude=-74.0060,
            Address="  New York, NY  "  # Extra spaces
        )
        
        assert webhook_input.Address == "New York, NY"

    def test_empty_address_becomes_none(self):
        """Test that empty address becomes None."""
        webhook_input = WebhookInput(
            Body="Location",
            From="whatsapp:+**********",
            Latitude=40.7128,
            Longitude=-74.0060,
            Address="   "  # Only spaces
        )
        
        assert webhook_input.Address is None

    def test_label_sanitization(self):
        """Test label field sanitization."""
        webhook_input = WebhookInput(
            Body="Location",
            From="whatsapp:+**********",
            Latitude=40.7128,
            Longitude=-74.0060,
            Label="  Empire State Building  "  # Extra spaces
        )
        
        assert webhook_input.Label == "Empire State Building"

    def test_empty_label_becomes_none(self):
        """Test that empty label becomes None."""
        webhook_input = WebhookInput(
            Body="Location",
            From="whatsapp:+**********",
            Latitude=40.7128,
            Longitude=-74.0060,
            Label=""  # Empty string
        )
        
        assert webhook_input.Label is None


class TestLocationMessageValidation:
    """Test message content validation with location data."""

    def test_native_location_allows_empty_body(self):
        """Test that native location messages allow empty body."""
        webhook_input = WebhookInput(
            Body="",  # Empty body should be allowed
            From="whatsapp:+**********",
            Latitude=40.7128,
            Longitude=-74.0060
        )
        
        assert webhook_input.Body == ""
        assert webhook_input.is_location_message() is True

    def test_partial_coordinates_require_body(self):
        """Test that partial coordinates still require body content."""
        with pytest.raises(ValidationError, match="Message body cannot be empty"):
            WebhookInput(
                Body="",
                From="whatsapp:+**********",
                Latitude=40.7128  # Missing longitude
            )

        with pytest.raises(ValidationError, match="Message body cannot be empty"):
            WebhookInput(
                Body="",
                From="whatsapp:+**********",
                Longitude=-74.0060  # Missing latitude
            )


class TestBackwardCompatibility:
    """Test backward compatibility with existing code."""

    def test_old_webhook_format_still_works(self):
        """Test that old webhook format without location fields still works."""
        # This simulates how existing code creates WebhookInput
        webhook_input = WebhookInput(
            Body="Hello",
            From="whatsapp:+**********",
            NumMedia=0
        )
        
        assert webhook_input.Body == "Hello"
        assert webhook_input.From == "whatsapp:+**********"
        assert webhook_input.NumMedia == 0
        assert webhook_input.Latitude is None
        assert webhook_input.Longitude is None
        assert webhook_input.is_location_message() is False
        assert webhook_input.is_voice_message() is False

    def test_existing_methods_unchanged(self):
        """Test that existing methods work unchanged."""
        # Voice message
        voice_input = WebhookInput(
            Body="",
            From="whatsapp:+**********",
            NumMedia=1,
            MediaUrl0="https://api.twilio.com/test",
            MediaContentType0="audio/ogg"
        )
        
        assert voice_input.is_voice_message() is True
        
        # Text message
        text_input = WebhookInput(
            Body="Hello",
            From="whatsapp:+**********"
        )
        
        assert text_input.is_voice_message() is False


class TestIntegrationWithLocationParser:
    """Test integration between WebhookInput and location parser."""

    def test_webhook_input_compatible_with_location_parser(self):
        """Test that WebhookInput works with location parser utilities."""
        from app.utils.location_parser import extract_location_data
        
        # Native location
        webhook_input = WebhookInput(
            Body="",
            From="whatsapp:+**********",
            Latitude=40.7128,
            Longitude=-74.0060,
            Address="New York, NY",
            Label="Empire State Building"
        )
        
        location_data = extract_location_data(webhook_input)
        
        assert location_data is not None
        assert location_data['type'] == 'native_whatsapp'
        assert location_data['latitude'] == 40.7128
        assert location_data['longitude'] == -74.0060

    def test_google_maps_url_compatible_with_parser(self):
        """Test that Google Maps URLs work with location parser."""
        from app.utils.location_parser import extract_location_data
        
        webhook_input = WebhookInput(
            Body="Check this: https://maps.google.com/?q=51.5074,-0.1278",
            From="whatsapp:+**********"
        )
        
        location_data = extract_location_data(webhook_input)
        
        assert location_data is not None
        assert location_data['type'] == 'google_maps_url'
        assert location_data['latitude'] == 51.5074
        assert location_data['longitude'] == -0.1278
