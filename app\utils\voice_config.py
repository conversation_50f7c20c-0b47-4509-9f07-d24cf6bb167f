"""
Voice Processing Configuration Management

This module provides centralized configuration management for voice message processing
features, including environment variable validation, feature flags, and configuration
validation following established patterns from the existing codebase.

Task 2.3 Implementation:
- Environment variable documentation and validation
- Feature flag support for gradual rollout
- Configuration validation in startup
- Integration with existing error handling framework
"""

import os
import logging
from dataclasses import dataclass
from typing import Optional, Dict, Any
from dotenv import load_dotenv

from app.utils.exceptions import (
    ConfigurationException, ErrorContext, ErrorSeverity, ErrorCategory
)
from app.utils.validation import MAX_AUDIO_FILE_SIZE_MB

logger = logging.getLogger(__name__)

# Voice processing configuration constants
DEFAULT_LANGUAGE_CODE = "en-US"
SUPPORTED_LANGUAGE_CODES = ["en-US", "ar-SA"]
DEFAULT_VOICE_PROCESSING_ENABLED = False
MIN_AUDIO_FILE_SIZE_MB = 1
MAX_AUDIO_FILE_SIZE_MB_LIMIT = 50  # Absolute maximum for safety
DEFAULT_TRANSCRIPTION_CACHE_TTL_MINUTES = 60  # Cache transcriptions for 1 hour

@dataclass
class VoiceProcessingConfig:
    """
    Voice processing configuration with validation and defaults.
    
    This class manages all voice processing related configuration including
    Google Cloud Speech-to-Text settings, feature flags, and audio processing
    parameters while maintaining 100% backward compatibility.
    """
    
    google_cloud_project_id: Optional[str] = None
    speech_to_text_language_code: str = DEFAULT_LANGUAGE_CODE
    voice_processing_enabled: bool = DEFAULT_VOICE_PROCESSING_ENABLED
    max_audio_file_size_mb: int = MAX_AUDIO_FILE_SIZE_MB
    transcription_cache_ttl_minutes: int = DEFAULT_TRANSCRIPTION_CACHE_TTL_MINUTES
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self._validate_configuration()
    
    def _validate_configuration(self):
        """Validate all configuration parameters."""
        # Validate language code
        if self.speech_to_text_language_code not in SUPPORTED_LANGUAGE_CODES:
            raise ConfigurationException(
                message=f"Invalid SPEECH_TO_TEXT_LANGUAGE_CODE: {self.speech_to_text_language_code}. "
                       f"Supported values: {', '.join(SUPPORTED_LANGUAGE_CODES)}",
                config_key="SPEECH_TO_TEXT_LANGUAGE_CODE",
                user_message="Voice processing language configuration is invalid. Please contact support."
            )
        
        # Validate audio file size
        if not isinstance(self.max_audio_file_size_mb, int) or self.max_audio_file_size_mb < MIN_AUDIO_FILE_SIZE_MB:
            raise ConfigurationException(
                message=f"Invalid MAX_AUDIO_FILE_SIZE_MB: {self.max_audio_file_size_mb}. "
                       f"Must be an integer >= {MIN_AUDIO_FILE_SIZE_MB}",
                config_key="MAX_AUDIO_FILE_SIZE_MB",
                user_message="Audio file size configuration is invalid. Please contact support."
            )
        
        if self.max_audio_file_size_mb > MAX_AUDIO_FILE_SIZE_MB_LIMIT:
            raise ConfigurationException(
                message=f"MAX_AUDIO_FILE_SIZE_MB too large: {self.max_audio_file_size_mb}MB. "
                       f"Maximum allowed: {MAX_AUDIO_FILE_SIZE_MB_LIMIT}MB",
                config_key="MAX_AUDIO_FILE_SIZE_MB",
                user_message="Audio file size configuration exceeds safety limits. Please contact support."
            )
        
        # Validate Google Cloud Project ID if voice processing is enabled
        if self.voice_processing_enabled and not self.google_cloud_project_id:
            raise ConfigurationException(
                message="GOOGLE_CLOUD_PROJECT_ID is required when VOICE_PROCESSING_ENABLED=true",
                config_key="GOOGLE_CLOUD_PROJECT_ID",
                user_message="Voice processing is enabled but Google Cloud configuration is missing. Please contact support."
            )

        # Validate transcription cache TTL
        if not isinstance(self.transcription_cache_ttl_minutes, int) or self.transcription_cache_ttl_minutes < 1:
            raise ConfigurationException(
                message=f"Invalid TRANSCRIPTION_CACHE_TTL_MINUTES: {self.transcription_cache_ttl_minutes}. "
                       f"Must be an integer >= 1",
                config_key="TRANSCRIPTION_CACHE_TTL_MINUTES",
                user_message="Transcription cache configuration is invalid. Please contact support."
            )
    
    @classmethod
    def from_env(cls, validate_required: bool = True) -> 'VoiceProcessingConfig':
        """
        Create configuration from environment variables with validation.
        
        Args:
            validate_required: Whether to validate required variables for voice processing
            
        Returns:
            VoiceProcessingConfig instance with validated settings
            
        Raises:
            ConfigurationException: If configuration is invalid
        """
        load_dotenv()
        
        # Get voice processing enabled flag
        voice_enabled_str = os.getenv('VOICE_PROCESSING_ENABLED', str(DEFAULT_VOICE_PROCESSING_ENABLED)).lower()
        voice_processing_enabled = voice_enabled_str in ('true', '1', 'yes', 'on')
        
        # Get Google Cloud Project ID
        google_cloud_project_id = os.getenv('GOOGLE_CLOUD_PROJECT_ID')
        
        # Get language code with validation
        language_code = os.getenv('SPEECH_TO_TEXT_LANGUAGE_CODE', DEFAULT_LANGUAGE_CODE)
        
        # Get audio file size limit with validation
        max_size_str = os.getenv('MAX_AUDIO_FILE_SIZE_MB', str(MAX_AUDIO_FILE_SIZE_MB))
        try:
            max_audio_file_size_mb = int(max_size_str)
        except ValueError:
            raise ConfigurationException(
                message=f"Invalid MAX_AUDIO_FILE_SIZE_MB value: '{max_size_str}'. Must be an integer.",
                config_key="MAX_AUDIO_FILE_SIZE_MB",
                user_message="Audio file size configuration is invalid. Please contact support."
            )

        # Get transcription cache TTL with validation
        cache_ttl_str = os.getenv('TRANSCRIPTION_CACHE_TTL_MINUTES', str(DEFAULT_TRANSCRIPTION_CACHE_TTL_MINUTES))
        try:
            transcription_cache_ttl_minutes = int(cache_ttl_str)
        except ValueError:
            raise ConfigurationException(
                message=f"Invalid TRANSCRIPTION_CACHE_TTL_MINUTES value: '{cache_ttl_str}'. Must be an integer.",
                config_key="TRANSCRIPTION_CACHE_TTL_MINUTES",
                user_message="Transcription cache configuration is invalid. Please contact support."
            )

        # Create configuration instance
        config = cls(
            google_cloud_project_id=google_cloud_project_id,
            speech_to_text_language_code=language_code,
            voice_processing_enabled=voice_processing_enabled,
            max_audio_file_size_mb=max_audio_file_size_mb,
            transcription_cache_ttl_minutes=transcription_cache_ttl_minutes
        )
        
        # Log configuration status
        if voice_processing_enabled:
            logger.info(f"Voice processing enabled: language={language_code}, "
                       f"max_size={max_audio_file_size_mb}MB, "
                       f"project_id={'configured' if google_cloud_project_id else 'missing'}")
        else:
            logger.info("Voice processing disabled by configuration")
        
        return config
    
    def is_voice_processing_available(self) -> bool:
        """
        Check if voice processing is available and properly configured.
        
        Returns:
            True if voice processing can be used, False otherwise
        """
        if not self.voice_processing_enabled:
            return False
        
        if not self.google_cloud_project_id:
            logger.warning("Voice processing enabled but GOOGLE_CLOUD_PROJECT_ID not configured")
            return False
        
        return True
    
    def get_speech_to_text_config(self) -> Dict[str, Any]:
        """
        Get configuration for Speech-to-Text service.
        
        Returns:
            Dictionary with Speech-to-Text configuration
        """
        return {
            'project_id': self.google_cloud_project_id,
            'language_code': self.speech_to_text_language_code,
            'enabled': self.voice_processing_enabled
        }
    
    def get_audio_processing_config(self) -> Dict[str, Any]:
        """
        Get configuration for audio processing.
        
        Returns:
            Dictionary with audio processing configuration
        """
        return {
            'max_file_size_mb': self.max_audio_file_size_mb,
            'enabled': self.voice_processing_enabled
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary for logging/debugging.
        
        Returns:
            Dictionary representation of configuration (sensitive data masked)
        """
        return {
            'google_cloud_project_id': 'configured' if self.google_cloud_project_id else None,
            'speech_to_text_language_code': self.speech_to_text_language_code,
            'voice_processing_enabled': self.voice_processing_enabled,
            'max_audio_file_size_mb': self.max_audio_file_size_mb,
            'transcription_cache_ttl_minutes': self.transcription_cache_ttl_minutes,
            'is_available': self.is_voice_processing_available()
        }


# Global configuration instance (lazy-loaded)
_voice_config: Optional[VoiceProcessingConfig] = None

def get_voice_config(reload: bool = False) -> VoiceProcessingConfig:
    """
    Get the global voice processing configuration instance.
    
    Args:
        reload: Whether to reload configuration from environment
        
    Returns:
        VoiceProcessingConfig instance
    """
    global _voice_config
    
    if _voice_config is None or reload:
        _voice_config = VoiceProcessingConfig.from_env()
    
    return _voice_config

def is_voice_processing_enabled() -> bool:
    """
    Quick check if voice processing is enabled and available.
    
    Returns:
        True if voice processing is enabled and properly configured
    """
    try:
        config = get_voice_config()
        return config.is_voice_processing_available()
    except Exception as e:
        logger.warning(f"Error checking voice processing configuration: {e}")
        return False

def validate_voice_processing_config() -> None:
    """
    Validate voice processing configuration and log results.
    
    This function can be called during application startup to ensure
    voice processing configuration is valid.
    
    Raises:
        ConfigurationException: If configuration is invalid
    """
    try:
        config = get_voice_config()
        logger.info(f"Voice processing configuration validated: {config.to_dict()}")
        
        if config.voice_processing_enabled and not config.is_voice_processing_available():
            logger.warning("Voice processing is enabled but not fully configured - some features may not work")
        
    except ConfigurationException as e:
        logger.error(f"Voice processing configuration validation failed: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error validating voice processing configuration: {e}")
        raise ConfigurationException(
            message=f"Voice processing configuration validation failed: {str(e)}",
            config_key="voice_processing",
            user_message="Voice processing configuration is invalid. Please contact support."
        )

def get_environment_variables_documentation() -> Dict[str, Dict[str, str]]:
    """
    Get documentation for all voice processing environment variables.
    
    Returns:
        Dictionary with variable documentation
    """
    return {
        'GOOGLE_CLOUD_PROJECT_ID': {
            'description': 'Google Cloud Project ID for Speech-to-Text API',
            'required': 'Required when VOICE_PROCESSING_ENABLED=true',
            'example': 'my-project-id',
            'default': 'None'
        },
        'SPEECH_TO_TEXT_LANGUAGE_CODE': {
            'description': 'Default language for speech recognition',
            'required': 'Optional',
            'example': 'en-US or ar-SA',
            'default': 'en-US'
        },
        'VOICE_PROCESSING_ENABLED': {
            'description': 'Feature flag to enable/disable voice processing',
            'required': 'Optional',
            'example': 'true, false, 1, 0',
            'default': 'false'
        },
        'MAX_AUDIO_FILE_SIZE_MB': {
            'description': 'Maximum allowed audio file size in megabytes',
            'required': 'Optional',
            'example': '16',
            'default': '16'
        },
        'TRANSCRIPTION_CACHE_TTL_MINUTES': {
            'description': 'Cache duration for transcription results in minutes',
            'required': 'Optional',
            'example': '60',
            'default': '60'
        }
    }
