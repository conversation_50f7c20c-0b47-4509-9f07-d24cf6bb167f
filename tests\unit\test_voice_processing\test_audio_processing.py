# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Unit tests for audio processing utilities.

Tests audio format validation, metadata extraction, quality assessment,
and preprocessing functionality for voice message processing.
"""

import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from typing import Generator

import pytest

from app.utils.audio_processing import (
    AudioProcessor, AudioMetadata, AudioProcessingResult,
    validate_audio_for_transcription, process_audio_for_transcription,
    SUPPORTED_AUDIO_FORMATS, DEFAULT_SAMPLE_RATE
)


@pytest.fixture
def audio_processor() -> AudioProcessor:
    """Create AudioProcessor instance for testing."""
    return AudioProcessor()


@pytest.fixture
def temp_audio_file() -> Generator[str, None, None]:
    """Create temporary audio file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
        # Write minimal OGG header for format validation
        temp_file.write(b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00')
        temp_file_path = temp_file.name
    
    yield temp_file_path
    
    # Cleanup
    if os.path.exists(temp_file_path):
        os.unlink(temp_file_path)


@pytest.fixture
def mock_pydub_audio():
    """Mock pydub AudioSegment for testing."""
    mock_audio = Mock()
    mock_audio.channels = 2
    mock_audio.frame_rate = 44100
    mock_audio.duration_seconds = 5.0
    mock_audio.set_channels.return_value = mock_audio
    mock_audio.set_frame_rate.return_value = mock_audio
    mock_audio.export = Mock()
    return mock_audio


class TestAudioProcessor:
    """Test cases for AudioProcessor class."""

    def test_init_with_pydub_available(self, audio_processor):
        """Test AudioProcessor initialization when pydub is available."""
        assert isinstance(audio_processor, AudioProcessor)
        # pydub_available depends on actual import, so we test both cases

    def test_init_without_pydub(self):
        """Test AudioProcessor initialization when pydub is not available."""
        with patch('app.utils.audio_processing.PYDUB_AVAILABLE', False):
            processor = AudioProcessor()
            assert not processor.pydub_available

    def test_validate_audio_format_supported(self, audio_processor, temp_audio_file):
        """Test audio format validation for supported formats."""
        result = audio_processor.validate_audio_format(temp_audio_file, 'audio/ogg')
        
        assert result.success
        assert result.format_valid
        assert result.content_type == 'audio/ogg'
        assert result.file_path == temp_audio_file

    def test_validate_audio_format_unsupported(self, audio_processor, temp_audio_file):
        """Test audio format validation for unsupported formats."""
        result = audio_processor.validate_audio_format(temp_audio_file, 'audio/wav')
        
        assert not result.success
        assert not result.format_valid
        assert 'Unsupported audio format' in result.error_message

    def test_validate_audio_format_file_not_found(self, audio_processor):
        """Test audio format validation for non-existent file."""
        result = audio_processor.validate_audio_format('/nonexistent/file.ogg', 'audio/ogg')
        
        assert not result.success
        assert 'File not found' in result.error_message

    def test_validate_audio_format_empty_file(self, audio_processor):
        """Test audio format validation for empty file."""
        with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
            temp_file_path = temp_file.name
        
        try:
            result = audio_processor.validate_audio_format(temp_file_path, 'audio/ogg')
            assert not result.success
            assert 'File is empty' in result.error_message
        finally:
            os.unlink(temp_file_path)

    def test_validate_audio_format_large_file(self, audio_processor):
        """Test audio format validation for oversized file."""
        with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as temp_file:
            # Write file larger than MAX_AUDIO_FILE_SIZE_MB (16MB)
            temp_file.write(b'OggS' + b'x' * (17 * 1024 * 1024))
            temp_file_path = temp_file.name
        
        try:
            result = audio_processor.validate_audio_format(temp_file_path, 'audio/ogg')
            assert not result.success
            assert 'File too large' in result.error_message
        finally:
            os.unlink(temp_file_path)

    @patch('app.utils.audio_processing.AudioSegment')
    def test_extract_metadata_with_pydub(self, mock_audiosegment, audio_processor, temp_audio_file, mock_pydub_audio):
        """Test metadata extraction when pydub is available."""
        mock_audiosegment.from_file.return_value = mock_pydub_audio
        
        result = audio_processor.extract_metadata(temp_audio_file, 'audio/ogg')
        
        assert result.success
        assert result.metadata.duration_seconds == 5.0
        assert result.metadata.channels == 2
        assert result.metadata.sample_rate == 44100

    def test_extract_metadata_without_pydub(self, audio_processor, temp_audio_file):
        """Test metadata extraction when pydub is not available."""
        with patch.object(audio_processor, 'pydub_available', False):
            result = audio_processor.extract_metadata(temp_audio_file, 'audio/ogg')
            
            assert result.success
            assert result.metadata.file_size > 0
            assert result.metadata.content_type == 'audio/ogg'
            # Duration should be None when pydub is not available
            assert result.metadata.duration_seconds is None

    @patch('app.utils.audio_processing.AudioSegment')
    def test_preprocess_for_transcription_with_pydub(self, mock_audiosegment, audio_processor, temp_audio_file, mock_pydub_audio):
        """Test audio preprocessing when pydub is available."""
        mock_audiosegment.from_file.return_value = mock_pydub_audio
        
        with tempfile.NamedTemporaryFile(suffix='.ogg', delete=False) as processed_file:
            mock_pydub_audio.export.return_value = None
            
            result = audio_processor.preprocess_for_transcription(temp_audio_file, 'audio/ogg')
            
            assert result.success
            assert result.processing_applied
            assert 'converted to mono' in result.processing_applied
            assert 'resampled' in result.processing_applied

    def test_preprocess_for_transcription_without_pydub(self, audio_processor, temp_audio_file):
        """Test audio preprocessing when pydub is not available."""
        with patch.object(audio_processor, 'pydub_available', False):
            result = audio_processor.preprocess_for_transcription(temp_audio_file, 'audio/ogg')
            
            assert result.success
            assert result.processed_file_path == temp_audio_file
            assert 'Preprocessing skipped' in result.processing_applied

    def test_assess_quality_good_audio(self, audio_processor, temp_audio_file):
        """Test quality assessment for good audio."""
        metadata = AudioMetadata(
            file_size=1024 * 1024,  # 1MB
            duration_seconds=30.0,
            content_type='audio/ogg',
            sample_rate=16000,
            channels=1
        )
        
        result = audio_processor.assess_quality(temp_audio_file, 'audio/ogg', metadata)
        
        assert result.success
        assert result.quality_score >= 0.7  # Good quality threshold

    def test_assess_quality_poor_audio(self, audio_processor, temp_audio_file):
        """Test quality assessment for poor audio."""
        metadata = AudioMetadata(
            file_size=1024,  # Very small file
            duration_seconds=1.0,  # Very short
            content_type='audio/ogg',
            sample_rate=8000,  # Low sample rate
            channels=1
        )
        
        result = audio_processor.assess_quality(temp_audio_file, 'audio/ogg', metadata)
        
        assert result.success
        assert result.quality_score < 0.5  # Poor quality threshold


class TestConvenienceFunctions:
    """Test cases for convenience functions."""

    @patch('app.utils.audio_processing.AudioProcessor')
    def test_validate_audio_for_transcription(self, mock_processor_class):
        """Test validate_audio_for_transcription convenience function."""
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        
        mock_result = AudioProcessingResult(success=True)
        mock_processor.validate_audio_format.return_value = mock_result
        
        result = validate_audio_for_transcription('/test/file.ogg', 'audio/ogg')
        
        assert result.success
        mock_processor.validate_audio_format.assert_called_once_with('/test/file.ogg', 'audio/ogg')

    @patch('app.utils.audio_processing.AudioProcessor')
    def test_process_audio_for_transcription(self, mock_processor_class):
        """Test process_audio_for_transcription convenience function."""
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        
        mock_result = AudioProcessingResult(success=True)
        mock_processor.preprocess_for_transcription.return_value = mock_result
        
        result = process_audio_for_transcription('/test/file.ogg', 'audio/ogg')
        
        assert result.success
        mock_processor.preprocess_for_transcription.assert_called_once_with('/test/file.ogg', 'audio/ogg')


class TestAudioMetadata:
    """Test cases for AudioMetadata dataclass."""

    def test_audio_metadata_creation(self):
        """Test AudioMetadata creation with all fields."""
        metadata = AudioMetadata(
            file_size=1024,
            duration_seconds=30.0,
            content_type='audio/ogg',
            sample_rate=16000,
            channels=1,
            file_path='/test/file.ogg'
        )
        
        assert metadata.file_size == 1024
        assert metadata.duration_seconds == 30.0
        assert metadata.content_type == 'audio/ogg'
        assert metadata.sample_rate == 16000
        assert metadata.channels == 1
        assert metadata.file_path == '/test/file.ogg'

    def test_audio_metadata_defaults(self):
        """Test AudioMetadata creation with default values."""
        metadata = AudioMetadata(
            file_size=1024,
            content_type='audio/ogg'
        )
        
        assert metadata.file_size == 1024
        assert metadata.content_type == 'audio/ogg'
        assert metadata.duration_seconds is None
        assert metadata.sample_rate is None
        assert metadata.channels is None
        assert metadata.file_path is None


class TestAudioProcessingResult:
    """Test cases for AudioProcessingResult dataclass."""

    def test_audio_processing_result_success(self):
        """Test AudioProcessingResult for successful processing."""
        result = AudioProcessingResult(
            success=True,
            processed_file_path='/test/processed.ogg',
            processing_applied='converted to mono, resampled to 16000Hz'
        )
        
        assert result.success
        assert result.processed_file_path == '/test/processed.ogg'
        assert result.processing_applied == 'converted to mono, resampled to 16000Hz'
        assert result.error_message is None

    def test_audio_processing_result_failure(self):
        """Test AudioProcessingResult for failed processing."""
        result = AudioProcessingResult(
            success=False,
            error_message='Processing failed: invalid format'
        )
        
        assert not result.success
        assert result.error_message == 'Processing failed: invalid format'
        assert result.processed_file_path is None
        assert result.processing_applied is None
