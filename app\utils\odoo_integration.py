import os
import re
import json
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv
from typing import Dict, List, Optional, Any
import logging
from dataclasses import dataclass
from contextlib import contextmanager
from app.utils.exceptions import (
    ConfigurationException, ExternalAPIException, ErrorContext,
    handle_external_api_error, log_exception
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Removed OdooConfig class - XML-RPC no longer supported

@dataclass
class OdooAPIConfig:
    """Configuration for Odoo.sh REST API integration."""
    base_url: str
    api_key: str

    @classmethod
    def from_env(cls) -> 'OdooAPIConfig':
        """Create configuration from environment variables."""
        load_dotenv()

        # Check for new API configuration first
        base_url = os.getenv('ODOO_API_BASE_URL')
        api_key = os.getenv('ODOO_API_KEY')

        if not base_url or not api_key:
            raise ConfigurationException(
                message="Missing required Odoo API environment variables: ODOO_API_BASE_URL, ODOO_API_KEY",
                config_key="ODOO_API_BASE_URL",
                user_message="Odoo API integration is not properly configured. Please contact support."
            )

        # Ensure URL format is correct
        if not base_url.startswith('https://'):
            base_url = 'https://' + base_url
        base_url = base_url.rstrip('/') + '/api'

        return cls(
            base_url=base_url,
            api_key=api_key
        )

# Removed DatabaseConfig class - using centralized DatabaseManager

# Removed OdooAPI class - XML-RPC no longer supported

class OdooRestAPI:
    """Wrapper for Odoo.sh REST API calls."""

    def __init__(self, config: OdooAPIConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'api-key': self.config.api_key,
            'Content-Type': 'application/json'
        })
        self._test_connection()

    def _test_connection(self):
        """Test API connection using the test endpoint."""
        try:
            response = self.session.get(f"{self.config.base_url}/test", timeout=30)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Successfully connected to Odoo API: {data.get('data', {}).get('message', 'Connected')}")
            else:
                raise ExternalAPIException(
                    message=f"API test failed with status {response.status_code}",
                    service="Odoo API",
                    user_message="Unable to connect to Odoo API. Please contact support.",
                    context=ErrorContext(operation="api_test")
                )
        except requests.exceptions.RequestException as e:
            raise ExternalAPIException(
                message=f"Failed to connect to Odoo API: {str(e)}",
                service="Odoo API",
                user_message="Unable to connect to Odoo API. Please contact support.",
                context=ErrorContext(operation="api_connection_test")
            )

    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """Make a request to the API with proper error handling."""
        try:
            url = f"{self.config.base_url}/{endpoint.lstrip('/')}"

            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, params=params, timeout=30)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data, params=params, timeout=30)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, params=params, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            # Parse response
            try:
                response_data = response.json()
            except ValueError as e:
                logger.error(f"Failed to parse JSON response: {str(e)}, Response text: {response.text[:500]}")
                response_data = {"status": response.status_code, "error": f"Invalid JSON response: {str(e)}"}

            # Check for API errors
            if response.status_code >= 400:
                error_msg = response_data.get('error', f'HTTP {response.status_code} error')
                logger.error(f"API Error - Status: {response.status_code}, URL: {url}, Response: {response.text[:1000]}")
                raise ExternalAPIException(
                    message=f"API request failed: {error_msg}",
                    service="Odoo API",
                    user_message="API request failed. Please try again or contact support.",
                    context=ErrorContext(
                        operation=f"{method} {endpoint}",
                        additional_data={"status_code": response.status_code, "response": response_data, "url": url}
                    )
                )

            return response_data

        except requests.exceptions.RequestException as e:
            raise ExternalAPIException(
                message=f"Network error during API request: {str(e)}",
                service="Odoo API",
                user_message="Network error occurred. Please check your connection and try again.",
                context=ErrorContext(operation=f"{method} {endpoint}")
            )

    # Booking API methods
    def get_bookings(self, customer_id: int = None, worker_id: int = None, state: str = None) -> List[Dict]:
        """Get bookings with optional filters."""
        params = {}
        if customer_id:
            params['customer_id'] = customer_id
        if worker_id:
            params['worker_id'] = worker_id
        if state:
            params['state'] = state

        response = self._make_request('GET', 'booking', params=params)
        return response.get('data', [])

    def get_booking(self, booking_id: int) -> Optional[Dict]:
        """Get a specific booking by ID."""
        response = self._make_request('GET', f'booking/{booking_id}')
        return response.get('data')

    def create_booking(self, booking_data: Dict) -> Optional[Dict]:
        """Create a new booking."""
        logger.info(f"Creating booking with data: {booking_data}")
        response = self._make_request('POST', 'booking', data=booking_data)
        return response.get('data')

    def update_booking(self, booking_id: int, booking_data: Dict) -> Optional[Dict]:
        """Update an existing booking."""
        response = self._make_request('PUT', f'booking/{booking_id}', data=booking_data)
        return response.get('data')

    def confirm_booking(self, booking_id: int) -> Optional[Dict]:
        """Confirm a booking."""
        response = self._make_request('POST', f'booking/{booking_id}/confirm')
        return response.get('data')

    def cancel_booking(self, booking_id: int) -> Optional[Dict]:
        """Cancel a booking."""
        response = self._make_request('POST', f'booking/{booking_id}/cancel')
        return response.get('data')

    # Customer API methods
    def get_customers(self, name: str = None, email: str = None, phone: str = None) -> List[Dict]:
        """Get customers with optional filters."""
        params = {}
        if name:
            params['name'] = name
        if email:
            params['email'] = email
        if phone:
            params['phone'] = phone

        response = self._make_request('GET', 'customer', params=params)
        return response.get('data', [])

    def get_customer(self, customer_id: int) -> Optional[Dict]:
        """Get a specific customer by ID."""
        response = self._make_request('GET', f'customer/{customer_id}')
        return response.get('data')

    def create_customer(self, customer_data: Dict) -> Optional[Dict]:
        """Create a new customer."""
        response = self._make_request('POST', 'customer', data=customer_data)
        return response.get('data')

    def update_customer(self, customer_id: int, customer_data: Dict) -> Optional[Dict]:
        """Update an existing customer."""
        response = self._make_request('PUT', f'customer/{customer_id}', data=customer_data)
        return response.get('data')

    # Worker API methods
    def get_workers(self, name: str = None, region_id: int = None, availability_status: str = None, skill_id: int = None) -> List[Dict]:
        """Get workers with optional filters."""
        params = {}
        if name:
            params['name'] = name
        if region_id:
            params['region_id'] = region_id
        if availability_status:
            params['availability_status'] = availability_status
        if skill_id:
            params['skill_id'] = skill_id

        response = self._make_request('GET', 'worker', params=params)
        return response.get('data', [])

    def get_worker(self, worker_id: int) -> Optional[Dict]:
        """Get a specific worker by ID."""
        response = self._make_request('GET', f'worker/{worker_id}')
        return response.get('data')

class OdooIntegration:
    """REST API only Odoo integration for hosted Odoo instances."""

    def __init__(self):
        """Initialize Odoo integration using REST API only."""
        self._init_db_connection()

        # Initialize REST API only
        try:
            logger.info("Initializing Odoo REST API integration...")
            api_config = OdooAPIConfig.from_env()
            self.odoo_rest = OdooRestAPI(api_config)
            logger.info("Successfully initialized Odoo REST API integration")
        except Exception as e:
            logger.error(f"Failed to initialize Odoo REST API: {str(e)}")
            raise ConfigurationException(
                message=f"Odoo REST API initialization failed: {str(e)}",
                config_key="ODOO_API_CONFIG",
                user_message="Failed to connect to Odoo API. Please check your configuration and contact support."
            )

    def _init_db_connection(self):
        """Initialize database manager - use centralized connection pooling."""
        try:
            from app.utils.database import DatabaseManager
            self.db_manager = DatabaseManager()
            logger.info("Successfully initialized database manager with connection pooling")
        except Exception as e:
            logger.warning(f"Database manager initialization failed: {str(e)}")
            logger.warning("Continuing without database connection - appointment processing will be limited")
            self.db_manager = None

    @contextmanager
    def get_db_cursor(self):
        """Context manager for database cursor using centralized connection pool."""
        if not self.db_manager:
            raise Exception("Database manager not available - cannot perform database operations")
        # Use the centralized DatabaseManager instead of direct connection
        with self.db_manager.get_cursor() as cursor:
            yield cursor

    def _map_appointment_to_booking_data(self, appointment: Dict) -> Dict:
        """
        Map appointment data to Odoo booking API format.

        Produces API-compliant datetime format as specified in custom_odoo_api_tests.json:
        - booking_date: "YYYY-MM-DD"
        - start_time: "YYYY-MM-DD HH:MM:SS"
        - end_time: "YYYY-MM-DD HH:MM:SS"

        Args:
            appointment: Dictionary containing appointment data

        Returns:
            Dictionary with booking_date, start_time, end_time in API format

        Raises:
            Uses Task 2.1 error handling framework for validation errors
        """
        try:
            # Extract and validate appointment date
            appointment_date = appointment.get('appointment_date')
            appointment_time = appointment.get('appointment_time')

            if not appointment_date or not appointment_time:
                # Use Task 2.1 error handling
                from app.utils.exceptions import ValidationException, ErrorContext
                raise ValidationException(
                    message="Missing required appointment date or time",
                    field="appointment_date_time",
                    context=ErrorContext(
                        operation="map_appointment_to_booking",
                        additional_data={"appointment_id": appointment.get('id')}
                    )
                )

            # Format date string (YYYY-MM-DD)
            if hasattr(appointment_date, 'strftime'):
                date_str = appointment_date.strftime('%Y-%m-%d')
            else:
                date_str = str(appointment_date)
                # Validate date format
                try:
                    datetime.strptime(date_str, '%Y-%m-%d')
                except ValueError:
                    logger.warning(f"Invalid date format: {date_str}, using current date")
                    date_str = datetime.now().strftime('%Y-%m-%d')

            # Clean and validate time string
            time_str = str(appointment_time).strip()

            # Handle different time formats: "15:00", "15:00:00", or "15"
            if time_str.count(':') == 2:
                time_str = ':'.join(time_str.split(':')[:2])  # Keep only HH:MM
            elif ':' not in time_str:
                # Handle hour-only format
                try:
                    hour = int(time_str)
                    if 0 <= hour <= 23:
                        time_str = f"{hour:02d}:00"
                    else:
                        raise ValueError(f"Invalid hour: {hour}")
                except ValueError:
                    logger.warning(f"Invalid time format: {time_str}, using 09:00")
                    time_str = "09:00"

            # Create start_time in API format: "YYYY-MM-DD HH:MM:SS"
            start_time = f"{date_str} {time_str}:00"

            # Calculate end time based on duration
            duration_minutes = appointment.get('duration', 60)  # Default 1 hour
            if isinstance(duration_minutes, str):
                try:
                    duration_minutes = int(duration_minutes)
                except ValueError:
                    logger.warning(f"Invalid duration: {duration_minutes}, using 60 minutes")
                    duration_minutes = 60

            # Ensure reasonable duration bounds (15 minutes to 8 hours)
            duration_minutes = max(15, min(duration_minutes, 480))

            try:
                start_dt = datetime.strptime(f"{date_str} {time_str}", '%Y-%m-%d %H:%M')
                end_dt = start_dt + timedelta(minutes=duration_minutes)
                end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                logger.warning(f"Error calculating end time: {str(e)}")
                # Fallback: add duration to start time string
                end_time = f"{date_str} {time_str}:00"

            booking_data = {
                'booking_date': date_str,
                'start_time': start_time,
                'end_time': end_time
            }

            logger.info(f"Mapped appointment to API-compliant booking data: {booking_data}")
            return booking_data

        except Exception as e:
            # Use Task 2.1 error handling for unexpected errors
            logger.error(f"Error mapping appointment to booking data: {str(e)}")
            context = ErrorContext(
                operation="map_appointment_to_booking",
                additional_data={
                    "appointment_id": appointment.get('id'),
                    "error_type": type(e).__name__
                }
            )
            log_exception(e, context=context)

            # Return safe defaults that match API format
            current_date = datetime.now().strftime('%Y-%m-%d')
            return {
                'booking_date': current_date,
                'start_time': f"{current_date} 09:00:00",
                'end_time': f"{current_date} 10:00:00"
            }

    def find_or_create_customer(self, appointment: Dict) -> Optional[int]:
        """
        Find or create customer using REST API with API-compliant field mapping.

        Maps local appointment data to Odoo API format as specified in custom_odoo_api_tests.json:
        - Uses 'gps_coordinates' instead of 'street' for location data
        - Includes proper phone number cleaning and validation
        - Follows Task 2.1 error handling patterns

        Args:
            appointment: Dictionary containing appointment data

        Returns:
            Customer ID if successful, None if failed
        """
        try:
            # Clean and validate phone number for search
            phone_number = appointment.get('phone_number', '')
            if not phone_number:
                from app.utils.exceptions import ValidationException, ErrorContext
                raise ValidationException(
                    message="Phone number is required for customer creation",
                    field="phone_number",
                    context=ErrorContext(
                        operation="find_or_create_customer",
                        additional_data={"appointment_id": appointment.get('id')}
                    )
                )

            # Clean WhatsApp prefix for API compatibility
            if phone_number.startswith('whatsapp:'):
                phone_number = phone_number.replace('whatsapp:', '')

            # Validate phone number format (basic validation)
            if not phone_number.startswith('+'):
                logger.warning(f"Phone number missing + prefix: {phone_number}")
                # Don't auto-add + as it might be incorrect, just log the warning

            logger.info(f"Searching for customer with phone: {phone_number}")

            # Search for existing customer by phone
            customers = self.odoo_rest.get_customers(phone=phone_number)
            if customers:
                customer_id = customers[0]['id']
                logger.info(f"Found existing customer with ID: {customer_id}")
                return customer_id

            # Create new customer with API-compliant field mapping
            customer_data = {
                'name': appointment.get('client_name', ''),
                'phone': phone_number,
                'gps_coordinates': self._convert_location_to_gps(appointment.get('location', ''))
            }

            # Validate required fields
            if not customer_data['name']:
                from app.utils.exceptions import ValidationException, ErrorContext
                raise ValidationException(
                    message="Client name is required for customer creation",
                    field="client_name",
                    context=ErrorContext(
                        operation="find_or_create_customer",
                        additional_data={"appointment_id": appointment.get('id')}
                    )
                )

            # Add optional fields if available (maintaining backward compatibility)
            if email := appointment.get('email'):
                customer_data['email'] = email

            logger.info(f"Creating new customer with API-compliant data: {customer_data}")
            customer = self.odoo_rest.create_customer(customer_data)
            if customer:
                customer_id = customer['id']
                logger.info(f"Created new customer with ID: {customer_id}")
                return customer_id
            else:
                logger.error("Failed to create customer - no data returned from API")
                return None

        except Exception as e:
            # Use Task 2.1 error handling framework
            context = ErrorContext(
                operation="find_or_create_customer",
                additional_data={
                    "appointment_id": appointment.get('id'),
                    "phone_number": appointment.get('phone_number', ''),
                    "error_type": type(e).__name__
                }
            )
            log_exception(e, context=context)
            logger.error(f"Error finding/creating customer: {str(e)}")
            return None

    def _convert_location_to_gps(self, location: str) -> str:
        """
        Enhanced to handle formatted location data from WhatsApp sharing (Task 7.5).

        Supports:
        1. Existing GPS format: "40.7128,-74.0060"
        2. WhatsApp formatted: "📍 40.7128, -74.0060"
        3. Full format: "Label - Address - 📍 40.7128, -74.0060"

        API expects: "latitude,longitude" format (e.g., "40.7128,-74.0060")

        Args:
            location: Location string from appointment

        Returns:
            GPS coordinates string or empty string if conversion not possible
        """
        try:
            if not location or not location.strip():
                return ""

            # Existing logic PRESERVED - Check if location is already in GPS format (lat,lng)
            if re.match(r'^-?\d+\.?\d*,-?\d+\.?\d*$', location.strip()):
                logger.info(f"Location already in GPS format: {location}")
                return location.strip()

            # NEW: Extract coordinates from WhatsApp formatted strings (Task 7.5)
            coord_pattern = r'📍\s*(-?\d+\.?\d*),\s*(-?\d+\.?\d*)'
            match = re.search(coord_pattern, location)
            if match:
                lat, lng = match.group(1), match.group(2)
                formatted_coords = f"{lat},{lng}"
                logger.info(f"Extracted coordinates from formatted location: {formatted_coords}")
                return formatted_coords

            # Existing fallback PRESERVED - For non-GPS locations
            logger.info(f"Location '{location}' not in GPS format, using empty coordinates")
            return ""

        except Exception as e:
            logger.warning(f"Error converting location to GPS: {str(e)}")
            return ""

    def _find_service_id(self, service_type: str) -> int:
        """
        Find service ID by service type name with validation against AVAILABLE_SERVICES.

        Maps service types to Odoo service IDs, ensuring consistency with the application's
        available services defined in app.tools.AVAILABLE_SERVICES.

        Args:
            service_type: Service type string from appointment

        Returns:
            Service ID for Odoo API, defaults to 1 for unknown services
        """
        try:
            # Import AVAILABLE_SERVICES to ensure consistency
            try:
                from app.tools import AVAILABLE_SERVICES
            except ImportError:
                logger.warning("Could not import AVAILABLE_SERVICES, using fallback validation")
                AVAILABLE_SERVICES = ["cleaning", "facial", "massage", "waxing"]

            # Validate and sanitize service type
            if not service_type or not isinstance(service_type, str):
                logger.warning(f"Invalid service type: {service_type}, using default")
                return 1

            service_type_clean = service_type.strip().lower()

            # Service mapping aligned with AVAILABLE_SERVICES only
            # Only includes services that exist in the application
            service_mapping = {
                'massage': 1,
                'cleaning': 2,
                'facial': 3,
                'waxing': 4
            }

            # Validate service type against available services
            if service_type_clean not in AVAILABLE_SERVICES:
                logger.warning(
                    f"Service type '{service_type}' not in AVAILABLE_SERVICES {AVAILABLE_SERVICES}. "
                    f"Using default service ID 1 (cleaning)."
                )
                # Use Task 2.1 error handling for validation
                from app.utils.exceptions import ValidationException, ErrorContext
                context = ErrorContext(
                    operation="find_service_id",
                    additional_data={
                        "invalid_service": service_type,
                        "available_services": AVAILABLE_SERVICES
                    }
                )
                # Log but don't raise - use fallback instead
                logger.warning(f"Invalid service type validation: {service_type}")
                return 1  # Default to cleaning service

            service_id = service_mapping.get(service_type_clean, 1)
            logger.info(f"Mapped valid service '{service_type}' to ID: {service_id}")
            return service_id

        except Exception as e:
            # Use Task 2.1 error handling
            context = ErrorContext(
                operation="find_service_id",
                additional_data={
                    "service_type": service_type,
                    "error_type": type(e).__name__
                }
            )
            log_exception(e, context=context)
            logger.error(f"Error finding service ID for '{service_type}': {str(e)}")
            return 1  # Safe default

    def _find_worker_id(self, appointment: Dict) -> int:
        """Find worker ID."""
        # If worker_id is specified in appointment, use it
        if worker_id := appointment.get('worker_id'):
            logger.info(f"Using specified worker ID: {worker_id}")
            return worker_id

        # Default worker ID (this should be configurable)
        default_worker_id = 1
        logger.info(f"Using default worker ID: {default_worker_id}")
        return default_worker_id



    def sync_appointment(self, appointment: Dict) -> Dict:
        """Sync a single appointment to Odoo via REST API."""
        try:
            logger.info(f"Starting sync for appointment: {appointment.get('id')}")

            # Step 1: Find or create customer
            customer_id = self.find_or_create_customer(appointment)
            if not customer_id:
                return {
                    'success': False,
                    'error': 'Failed to find/create customer',
                    'appointment_id': appointment.get('id')
                }

            # Step 2: Get service and worker IDs
            service_id = self._find_service_id(appointment['service_type'])
            worker_id = self._find_worker_id(appointment)

            # Step 3: Map appointment data to booking format
            booking_time_data = self._map_appointment_to_booking_data(appointment)

            # Step 4: Create booking data
            booking_data = {
                'customer_id': customer_id,
                'worker_id': worker_id,
                'service_id': service_id,
                **booking_time_data
            }

            logger.info(f"Creating booking with data: {booking_data}")

            # Step 5: Create booking
            booking = self.odoo_rest.create_booking(booking_data)
            if not booking:
                return {
                    'success': False,
                    'error': 'Failed to create booking',
                    'appointment_id': appointment.get('id')
                }

            booking_id = booking['id']
            logger.info(f"Successfully created booking {booking_id} for appointment {appointment.get('id')}")

            # Step 6: Mark appointment as processed
            self._mark_appointment_processed(appointment.get('id'), booking_id)

            return {
                'success': True,
                'booking_id': booking_id,
                'customer_id': customer_id,
                'appointment_id': appointment.get('id')
            }

        except Exception as e:
            logger.error(f"Error syncing appointment {appointment.get('id')}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'appointment_id': appointment.get('id')
            }

    def _build_description(self, appointment: Dict) -> str:
        """Build description for Odoo records."""
        parts = [
            f"Service: {appointment['service_type']}",
            f"Date: {appointment['appointment_date']}",
            f"Time: {appointment['appointment_time']}"
        ]

        # Add location if available
        if location := appointment.get('location'):
            parts.append(f"Location: {location}")

        # Add duration if available
        if duration := appointment.get('duration'):
            parts.append(f"Duration: {duration} minutes")

        # Add recurring information if available
        if appointment.get('is_recurring'):
            parts.append("Type: Recurring Appointment")
            if pattern := appointment.get('recurrence_pattern'):
                try:
                    pattern_data = json.loads(pattern) if isinstance(pattern, str) else pattern
                    parts.append(f"Recurrence: {pattern_data.get('frequency', 'Unknown')} every {pattern_data.get('interval', 1)} week(s)")
                except Exception as e:
                    logger.warning(f"Error parsing recurrence pattern: {str(e)}")
                    parts.append("Recurrence: Pattern not available")

        # Add client information
        parts.append(f"Client: {appointment['client_name']}")
        parts.append(f"Phone: {appointment['phone_number']}")

        return "\n".join(parts)

    def _get_unprocessed_appointments(self) -> List[Dict]:
        """Get unprocessed appointments with client information."""
        try:
            with self.get_db_cursor() as cur:
                cur.execute("""
                    SELECT a.*, c.location, c.contract_type, c.most_asked_provider
                    FROM appointments a
                    LEFT JOIN clients c ON a.phone_number = c.phone_number
                    WHERE a.processed = FALSE
                    ORDER BY a.created_at ASC
                """)
                return [dict(zip([col[0] for col in cur.description], row))
                       for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Error fetching unprocessed appointments: {str(e)}")
            return []

# Removed old XML-RPC methods - no longer needed for REST API only integration

    def _mark_appointment_processed(self, appointment_id: int, booking_id: int):
        """Mark appointment as processed in PostgreSQL."""
        try:
            with self.get_db_cursor() as cur:
                cur.execute("""
                    UPDATE appointments
                    SET processed = TRUE,
                        processed_at = CURRENT_TIMESTAMP,
                        odoo_lead_id = %s
                    WHERE id = %s
                """, (booking_id, appointment_id))
                logger.info(f"Marked appointment {appointment_id} as processed with booking ID {booking_id}")
        except Exception as e:
            logger.error(f"Error marking appointment as processed: {str(e)}")
            raise

    def process_appointments(self) -> Dict:
        """Process unprocessed appointments and sync with Odoo via REST API."""
        try:
            appointments = self._get_unprocessed_appointments()
            processed_count = 0
            errors = []

            logger.info(f"Found {len(appointments)} unprocessed appointments")

            for appointment in appointments:
                try:
                    # Validate required fields
                    required_fields = ['client_name', 'phone_number', 'service_type',
                                     'appointment_date', 'appointment_time']
                    if missing := [f for f in required_fields if f not in appointment]:
                        errors.append(f"Missing required fields for appointment {appointment.get('id')}: {missing}")
                        continue

                    # Sync appointment via REST API
                    result = self.sync_appointment(appointment)
                    if result['success']:
                        processed_count += 1
                        logger.info(f"Successfully processed appointment {appointment['id']}")
                    else:
                        errors.append(f"Failed to process appointment {appointment.get('id')}: {result['error']}")

                except Exception as e:
                    errors.append(f"Error processing appointment {appointment.get('id')}: {str(e)}")
                    continue

            return {
                "processed": processed_count,
                "errors": errors,
                "message": f"Processed {processed_count} appointments via REST API" +
                          (f" with {len(errors)} errors" if errors else "")
            }

        except Exception as e:
            logger.error(f"Error in process_appointments: {str(e)}")
            return {"processed": 0, "message": str(e)}

    # REST API only helper methods
    def get_customer_by_phone(self, phone_number: str) -> Optional[Dict]:
        """Get customer by phone number via REST API."""
        try:
            # Clean phone number
            if phone_number.startswith('whatsapp:'):
                phone_number = phone_number.replace('whatsapp:', '')

            customers = self.odoo_rest.get_customers(phone=phone_number)
            return customers[0] if customers else None
        except Exception as e:
            logger.error(f"Error getting customer by phone: {str(e)}")
            return None

    def get_booking_by_id(self, booking_id: int) -> Optional[Dict]:
        """Get booking by ID via REST API."""
        try:
            return self.odoo_rest.get_booking(booking_id)
        except Exception as e:
            logger.error(f"Error getting booking by ID: {str(e)}")
            return None

if __name__ == "__main__":
    # Test script for REST API integration
    try:
        odoo = OdooIntegration()
        results = odoo.process_appointments()
        print(f"Processed {results['processed']} appointments")
        if results.get('errors'):
            print("Errors:", results['errors'])
    except Exception as e:
        print(f"Error: {str(e)}")

# End of file