# Voice Processing Environment Variables

This document provides comprehensive documentation for all environment variables related to voice message processing functionality in the WhatsApp booking agent.

## Overview

Voice processing functionality allows the agent to handle voice messages from WhatsApp users by transcribing them to text and processing them through the existing booking workflow. This feature is controlled by environment variables that provide configuration flexibility and feature flag support for gradual rollout.

## Environment Variables

### Required Variables (when voice processing is enabled)

#### `GOOGLE_CLOUD_PROJECT_ID`
- **Description**: Google Cloud Project ID for Speech-to-Text API access
- **Required**: Yes, when `VOICE_PROCESSING_ENABLED=true`
- **Default**: None
- **Example**: `my-project-id`
- **Validation**: Must be a valid Google Cloud project ID
- **Notes**: 
  - Required for Google Cloud Speech-to-Text API authentication
  - Must have Speech-to-Text API enabled
  - Service account must have appropriate permissions

### Optional Variables

#### `VOICE_PROCESSING_ENABLED`
- **Description**: Feature flag to enable/disable voice processing functionality
- **Required**: No
- **Default**: `false`
- **Valid Values**: `true`, `false`, `1`, `0`, `yes`, `no`, `on`, `off`
- **Example**: `VOICE_PROCESSING_ENABLED=true`
- **Notes**:
  - Controls whether voice messages are processed or receive fallback responses
  - When `false`, voice messages receive a polite fallback message
  - When `true`, requires `GOOGLE_CLOUD_PROJECT_ID` to be configured

#### `SPEECH_TO_TEXT_LANGUAGE_CODE`
- **Description**: Default language code for speech recognition
- **Required**: No
- **Default**: `en-US`
- **Valid Values**: `en-US`, `ar-SA`
- **Example**: `SPEECH_TO_TEXT_LANGUAGE_CODE=ar-SA`
- **Notes**:
  - Determines the primary language for speech recognition
  - Supports English (US) and Arabic (Saudi Arabia)
  - Can be overridden per request based on user context

#### `MAX_AUDIO_FILE_SIZE_MB`
- **Description**: Maximum allowed audio file size in megabytes
- **Required**: No
- **Default**: `16`
- **Valid Range**: `1` to `50`
- **Example**: `MAX_AUDIO_FILE_SIZE_MB=20`
- **Notes**:
  - WhatsApp default limit is 16MB
  - Higher values may impact performance
  - Must be an integer value

## Configuration Examples

### Development Environment
```bash
# Enable voice processing for development
VOICE_PROCESSING_ENABLED=true
GOOGLE_CLOUD_PROJECT_ID=my-dev-project
SPEECH_TO_TEXT_LANGUAGE_CODE=en-US
MAX_AUDIO_FILE_SIZE_MB=16
```

### Production Environment
```bash
# Production configuration with Arabic support
VOICE_PROCESSING_ENABLED=true
GOOGLE_CLOUD_PROJECT_ID=my-prod-project
SPEECH_TO_TEXT_LANGUAGE_CODE=ar-SA
MAX_AUDIO_FILE_SIZE_MB=16
```

### Disabled Voice Processing
```bash
# Voice processing disabled (default)
VOICE_PROCESSING_ENABLED=false
# Other voice processing variables are ignored when disabled
```

## Configuration Validation

The application validates voice processing configuration during startup:

1. **Startup Validation**: Configuration is validated when the application starts
2. **Required Dependencies**: When voice processing is enabled, `GOOGLE_CLOUD_PROJECT_ID` must be provided
3. **Value Validation**: All values are validated for correct format and range
4. **Graceful Degradation**: Invalid configuration results in voice processing being disabled with warnings

### Validation Errors

Common configuration errors and solutions:

| Error | Cause | Solution |
|-------|-------|----------|
| `Invalid SPEECH_TO_TEXT_LANGUAGE_CODE` | Unsupported language code | Use `en-US` or `ar-SA` |
| `Invalid MAX_AUDIO_FILE_SIZE_MB` | Non-integer or out of range | Use integer between 1 and 50 |
| `GOOGLE_CLOUD_PROJECT_ID required` | Missing project ID when enabled | Set valid Google Cloud project ID |

## Feature Flag Behavior

### When `VOICE_PROCESSING_ENABLED=true`
- Voice messages are processed through the speech-to-text pipeline
- Requires proper Google Cloud configuration
- Users receive processing status updates
- Fallback to text request if processing fails

### When `VOICE_PROCESSING_ENABLED=false` (default)
- Voice messages receive polite fallback responses
- No Google Cloud dependencies required
- Users are asked to send text messages instead
- Existing text message functionality unaffected

## Integration Points

### Application Startup
- Configuration validation in `app/main.py`
- Graceful degradation if configuration is invalid
- Logging of configuration status

### Webhook Processing
- Feature flag check in voice message detection
- Appropriate routing based on configuration
- Different user messages based on processing availability

### Speech-to-Text Service
- Uses `GOOGLE_CLOUD_PROJECT_ID` for API authentication
- Respects `SPEECH_TO_TEXT_LANGUAGE_CODE` for recognition
- Validates audio files against `MAX_AUDIO_FILE_SIZE_MB`

## Security Considerations

### Sensitive Information
- `GOOGLE_CLOUD_PROJECT_ID` is logged as "configured" or "missing" (not the actual value)
- Service account credentials should be managed separately
- Audio files are processed securely and cleaned up automatically

### Access Control
- Google Cloud service account requires Speech-to-Text API permissions
- Audio file size limits prevent abuse
- Temporary file handling with secure permissions

## Monitoring and Logging

### Configuration Logging
```
INFO: Voice processing configuration validated: {
  "google_cloud_project_id": "configured",
  "speech_to_text_language_code": "en-US",
  "voice_processing_enabled": true,
  "max_audio_file_size_mb": 16,
  "is_available": true
}
```

### Runtime Logging
- Voice message detection and routing decisions
- Processing status (enabled/disabled) in all voice message logs
- Configuration validation results during startup

## Troubleshooting

### Common Issues

1. **Voice processing not working despite being enabled**
   - Check `GOOGLE_CLOUD_PROJECT_ID` is set correctly
   - Verify Google Cloud Speech-to-Text API is enabled
   - Ensure service account has proper permissions

2. **Configuration validation fails at startup**
   - Check all environment variable values are valid
   - Review startup logs for specific validation errors
   - Ensure required variables are set when voice processing is enabled

3. **Audio files rejected**
   - Check file size against `MAX_AUDIO_FILE_SIZE_MB` limit
   - Verify audio format is supported (see validation.py)
   - Review audio processing logs for specific errors

### Debug Mode
Enable debug logging to see detailed configuration information:
```bash
# Set logging level for detailed configuration debugging
LOGGING_LEVEL=DEBUG
```

## Migration Guide

### Enabling Voice Processing
1. Set `VOICE_PROCESSING_ENABLED=true`
2. Configure `GOOGLE_CLOUD_PROJECT_ID` with valid project
3. Optionally set `SPEECH_TO_TEXT_LANGUAGE_CODE` for your primary language
4. Test with sample voice messages
5. Monitor logs for any configuration issues

### Disabling Voice Processing
1. Set `VOICE_PROCESSING_ENABLED=false`
2. Other voice processing variables can be left as-is (they will be ignored)
3. Voice messages will receive fallback responses

## Related Documentation

- [Voice Message Implementation Plan](VOICE_MESSAGE_IMPLEMENTATION.md)
- [Main README](README.md)
- [Deployment Guide](deployment/README.md)
- [Google Cloud Speech-to-Text Documentation](https://cloud.google.com/speech-to-text/docs)
