# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_google_genai import Chat<PERSON>oo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langgraph.graph import END, MessagesState, StateGraph
from langgraph.prebuilt import ToolNode
import re
import logging
import json
from psycopg2.extras import RealDictCursor

from app.tools import (
    tools,
    get_booking_context,
    clear_booking_context,
    extract_date,
    extract_time,
    AVAILABLE_SERVICES,
    book_appointment,
    sync_appointment_to_odoo,
    check_availability
)
from app.utils.date_parser import extract_and_parse_date, get_current_date_context
from app.utils.database import DatabaseManager
from app.utils.odoo_integration import OdooIntegration
from app.utils.language import detect_language, translate_response
from app.utils.exceptions import (
    ExternalAPIException, ErrorContext, log_exception, sanitize_error_for_user
)
from app.utils.cache import get_cached_llm_response, cache_llm_response
from app.prompts import (
    EXTRACT_INFO_PROMPT,
    GENERATE_RESPONSE_PROMPT,
    BOOKING_CONFIRMATION_PROMPT,
    ODOO_SYNC_DECISION_PROMPT,
    TRANSLATE_PROMPT
)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('agent.log')
    ]
)
logger = logging.getLogger(__name__)

class BookingAgent:
    """Main agent class for handling appointment bookings and Odoo synchronization."""

    def __init__(self):
        """Initialize the agent with required components."""
        self.db = DatabaseManager()
        self.odoo = OdooIntegration()
        self.llm = self._initialize_llm()
        self.booking_contexts = {}

    def _initialize_llm(self) -> ChatGoogleGenerativeAI:
        """Initialize the language model with appropriate settings."""
        return ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            google_api_key=os.getenv('GOOGLE_API_KEY'),
            temperature=0.3,
            max_tokens=1024
        ).bind_tools(tools)

    def _is_greeting_message(self, message: str) -> bool:
        """Simple greeting detection using keyword matching."""
        greeting_keywords = [
            # English greetings
            'hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening',
            # Arabic greetings
            'مرحبا', 'أهلا', 'السلام عليكم', 'صباح الخير', 'مساء الخير'
        ]
        message_lower = message.lower().strip()
        return any(keyword in message_lower for keyword in greeting_keywords)

    def _extract_booking_info(self, message: str, conversation_history: List[Dict]) -> Dict:
        """Extract booking information from the message using LLM."""
        try:
            # Detect language
            language = detect_language(message)
            logger.debug(f"Detected language: {language}")

            # Ensure conversation_history is a list
            if not isinstance(conversation_history, list):
                conversation_history = []

            # Get current booking context
            booking_context = {}
            if conversation_history:
                for msg in reversed(conversation_history):
                    if msg.get('role') == 'assistant' and 'booking_context' in msg:
                        booking_context = msg['booking_context']
                        break

            # Update booking context with language
            booking_context['language'] = language

            # Get current date context for accurate relative date parsing
            date_context = get_current_date_context()

            prompt = EXTRACT_INFO_PROMPT.format(
                message=message,
                booking_context=booking_context,
                available_services=', '.join(AVAILABLE_SERVICES),
                current_date=date_context['current_date'],
                current_year=date_context['current_year']
            )

            # Check cache first for LLM response
            cache_context = {
                'language': language,
                'operation_type': 'extract_booking_info',
                'service_type': booking_context.get('service_type')
            }

            cached_response = get_cached_llm_response(prompt, cache_context)
            if cached_response:
                logger.debug("Using cached LLM response for booking info extraction")
                response_content = cached_response
            else:
                # Make LLM call and cache the result
                response = self.llm.invoke(prompt)
                response_content = response.content if hasattr(response, 'content') else str(response)
                cache_llm_response(prompt, cache_context, response_content)

            # Clean and parse the response
            response_content = response_content.strip()
            if response_content.startswith('```json'):
                response_content = response_content[7:]
            if response_content.endswith('```'):
                response_content = response_content[:-3]
            response_content = response_content.strip()
            response_content = ' '.join(response_content.split())

            try:
                extracted_info = json.loads(response_content)
                extracted_info['language'] = language

                # Add fallback date parsing if LLM didn't extract a date but text contains relative expressions
                if 'appointment_date' not in extracted_info or not extracted_info['appointment_date']:
                    fallback_date = extract_and_parse_date(message, datetime.now())
                    if fallback_date:
                        extracted_info['appointment_date'] = fallback_date
                        logger.info(f"Fallback date parsing extracted: {fallback_date}")

                return {k: v for k, v in extracted_info.items() if v is not None and v != ""}
            except json.JSONDecodeError:
                json_start = response_content.find('{')
                json_end = response_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = response_content[json_start:json_end]
                    json_str = json_str.strip().strip('"\'')
                    json_str = ' '.join(json_str.split())
                    try:
                        extracted_info = json.loads(json_str)
                        extracted_info['language'] = language

                        # Add fallback date parsing for second JSON attempt as well
                        if 'appointment_date' not in extracted_info or not extracted_info['appointment_date']:
                            fallback_date = extract_and_parse_date(message, datetime.now())
                            if fallback_date:
                                extracted_info['appointment_date'] = fallback_date
                                logger.info(f"Fallback date parsing extracted (second attempt): {fallback_date}")

                        return {k: v for k, v in extracted_info.items() if v is not None and v != ""}
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing extracted JSON: {str(e)}")
                        return {'language': language}
                else:
                    logger.error("No JSON object found in response")
                    return {'language': language}

        except Exception as e:
            # Use standardized error handling for LLM errors
            context = ErrorContext(
                operation="extract_booking_info",
                additional_data={"message_length": len(message)}
            )
            log_exception(e, context=context)
            return {'language': language}

    def _generate_response(self, message: str, booking_info: Dict, conversation_history: List[Dict]) -> str:
        """Generate a natural response based on conversation context."""
        try:
            language = booking_info.get('booking_context', {}).get('language', 'en')
            prompt = GENERATE_RESPONSE_PROMPT.format(
                conversation_history=conversation_history[-5:] if len(conversation_history) > 5 else conversation_history,
                message=message,
                booking_context=booking_info.get('booking_context', {}),
                missing_fields=booking_info.get('missing_fields', [])
            )

            # Check cache first for LLM response
            cache_context = {
                'language': language,
                'operation_type': 'generate_response'
            }

            cached_response = get_cached_llm_response(prompt, cache_context)
            if cached_response:
                logger.debug("Using cached LLM response for response generation")
                return translate_response(cached_response, language)
            else:
                # Make LLM call and cache the result
                response = self.llm.invoke(prompt)
                response_content = response.content if hasattr(response, 'content') else str(response)
                cache_llm_response(prompt, cache_context, response_content)
                return translate_response(response_content, language)
        except Exception as e:
            # Use standardized error handling for response generation
            context = ErrorContext(
                operation="generate_response",
                additional_data={"message_length": len(message)}
            )
            log_exception(e, context=context)
            language = booking_info.get('booking_context', {}).get('language', 'en')
            error_response = sanitize_error_for_user(e)
            return translate_response(error_response, language)

    def _check_booking_confirmation(self, message: str, booking_context: Dict, conversation_history: List[Dict]) -> Dict:
        """Check if the booking is confirmed and generate appropriate response."""
        try:
            language = booking_context.get('language', 'en')
            confirmation_keywords = ['okay', 'yes', 'sure', 'confirm', 'book', 'schedule', 'appointment', 'نعم', 'تأكيد', 'حسنًا']
            is_confirmation = any(keyword in message.lower() for keyword in confirmation_keywords)

            required_fields = ['client_name', 'service_type', 'appointment_date', 'appointment_time', 'location']
            missing_fields = [
                field for field in required_fields
                if field not in booking_context or
                booking_context[field] is None or
                (isinstance(booking_context[field], str) and booking_context[field].strip() == "")
            ]

            if missing_fields:
                logger.debug(f"Missing or invalid fields: {missing_fields}")
                response = f"To complete your booking, I need the following information: {', '.join(missing_fields)}"
                return {
                    "is_confirmed": False,
                    "response": translate_response(response, language),
                    "needs_clarification": True,
                    "clarification_question": translate_response(
                        f"Could you please provide the missing information: {', '.join(missing_fields)}?", language
                    )
                }

            if is_confirmation:
                logger.info("Confirmation detected, proceeding with booking")
                response = "I'll proceed with booking your appointment."
                return {
                    "is_confirmed": True,
                    "response": translate_response(response, language),
                    "needs_clarification": False,
                    "clarification_question": None
                }

            response = (
                f"I have all the information for your appointment:\n"
                f"Name: {booking_context['client_name'] or 'Not provided'}\n"
                f"Service: {booking_context['service_type'] or 'Not provided'}\n"
                f"Date: {booking_context['appointment_date'] or 'Not provided'}\n"
                f"Time: {booking_context['appointment_time'] or 'Not provided'}\n"
                f"Location: {booking_context['location'] or 'Not provided'}\n\n"
                f"Would you like me to confirm this booking?"
            )
            return {
                "is_confirmed": False,
                "response": translate_response(response, language),
                "needs_clarification": True,
                "clarification_question": translate_response(
                    "Please confirm if you want to proceed with this booking.", language
                )
            }

        except Exception as e:
            language = booking_context.get('language', 'en')
            logger.error(f"Error checking booking confirmation: {str(e)}")
            error_response = "I apologize, but I encountered an error processing your request."
            return {
                "is_confirmed": False,
                "response": translate_response(error_response, language),
                "needs_clarification": True,
                "clarification_question": translate_response("Could you please try again?", language)
            }

    def _process_booking(self, phone_number: str, booking_context: Dict) -> str:
        """Process the booking and sync with Odoo."""
        try:
            language = booking_context.get('language', 'en')
            logger.info(f"Starting booking process for {booking_context.get('client_name')}")

            logger.info(f"Checking availability for {booking_context['appointment_date']}")
            availability_result = check_availability.invoke({
                'date': booking_context['appointment_date'],
                'phone_number': phone_number
            })
            logger.debug(f"Raw availability result: {availability_result}, type: {type(availability_result)}")

            available_slots = []
            if isinstance(availability_result, str):
                if "Available time slots" in availability_result:
                    slots = availability_result.split('\n')
                    available_slots = [
                        slot.strip() for slot in slots
                        if slot.strip() and re.match(r'^\d{2}:\d{2}$', slot.strip())
                    ]
                elif "No available slots" in availability_result:
                    available_slots = []
                else:
                    slots = availability_result.split('\n')
                    available_slots = [
                        slot.strip() for slot in slots
                        if slot.strip() and re.match(r'^\d{2}:\d{2}$', slot.strip())
                    ]
            elif isinstance(availability_result, list):
                available_slots = [
                    slot for slot in availability_result
                    if isinstance(slot, str) and re.match(r'^\d{2}:\d{2}$', slot)
                ]
            logger.debug(f"Parsed available slots: {available_slots}")

            if not available_slots:
                logger.warning(f"No available slots for {booking_context['appointment_date']}")
                response = f"Sorry, there are no available slots for {booking_context['appointment_date']}. Please try another date."
                return translate_response(response, language)

            requested_time = booking_context.get('appointment_time')
            if not requested_time or requested_time not in available_slots:
                logger.warning(f"Requested time {requested_time} is not available")
                response = (
                    f"Sorry, the time {requested_time} is already booked for {booking_context['appointment_date']}. "
                    f"Available slots: {', '.join(available_slots)}. Please choose another time."
                )
                return translate_response(response, language)

            client_data = {
                'client_name': booking_context['client_name'],
                'phone_number': phone_number,
                'location': booking_context.get('location'),
                'contract_type': booking_context.get('contract_type', 'one-time'),
                'service_type': booking_context.get('service_type')
            }

            logger.info(f"Attempting to save client data: {client_data}")
            try:
                client_id = self.db.save_client(client_data)
                if client_id == -1:
                    logger.warning("Client table permission denied - continuing with booking without client record")
                elif client_id:
                    logger.info(f"Client information saved successfully with ID: {client_id}")
                else:
                    logger.warning("Client save returned no ID - continuing with booking")
            except Exception as e:
                logger.error(f"Error saving client information: {str(e)}")
                # Don't let client save errors break the booking flow

            try:
                logger.info("Attempting to book appointment...")
                booking_result = book_appointment.invoke({
                    'client_name': booking_context['client_name'],
                    'phone_number': phone_number,
                    'service_type': booking_context['service_type'],
                    'appointment_date': booking_context['appointment_date'],
                    'appointment_time': booking_context['appointment_time'],
                    'location': booking_context['location']
                })

                logger.info(f"Booking result: {booking_result}")

                appointment_id = None
                # Extract ID using regex to handle both English and Arabic
                id_match = re.search(r'(?:ID|الرقم المرجعي):?\s*(\d+|[٠-٩]+)', booking_result)
                if id_match:
                    id_str = id_match.group(1)
                    # Convert Arabic numerals to Latin if necessary
                    arabic_to_latin = str.maketrans('٠١٢٣٤٥٦٧٨٩', '0123456789')
                    id_str = id_str.translate(arabic_to_latin)
                    appointment_id = int(id_str)
                    logger.info(f"Extracted appointment ID: {appointment_id}")

                if appointment_id:
                    try:
                        saved_appointment = self.db.get_appointment_by_id(appointment_id)
                        if saved_appointment:
                            logger.info(f"Verified appointment saved in database: {saved_appointment}")
                        else:
                            logger.error(f"Appointment {appointment_id} not found in database after saving")
                    except Exception as verify_error:
                        logger.error(f"Error verifying appointment: {str(verify_error)}")

                    try:
                        logger.info(f"Triggering Odoo sync for appointment {appointment_id}")
                        sync_result = sync_appointment_to_odoo.invoke({
                            'appointment_id': appointment_id,
                            'phone_number': phone_number
                        })
                        logger.info(f"Odoo sync result: {sync_result}")
                    except Exception as sync_error:
                        logger.error(f"Error syncing to Odoo: {str(sync_error)}")

                clear_booking_context(phone_number)
                logger.info("Booking context cleared")

                return booking_result

            except Exception as booking_error:
                logger.error(f"Error booking appointment: {str(booking_error)}")
                response = f"Sorry, there was an error booking your appointment: {str(booking_error)}. Please try again or contact support."
                return translate_response(response, language)

        except Exception as e:
            language = booking_context.get('language', 'en')
            logger.error(f"Error processing booking: {str(e)}")
            response = f"Sorry, I couldn't process your booking due to an error: {str(e)}. Please try a different time or date, or contact support."
            return translate_response(response, language)

    def process_message(self, message: str, phone_number: str) -> str:
        """Main method to process incoming messages and handle booking flow."""
        try:
            booking_context = get_booking_context(phone_number)
            if 'conversation_history' not in booking_context:
                booking_context['conversation_history'] = []

            # Add simple greeting detection for new conversations
            is_new_conversation = len(booking_context['conversation_history']) == 0
            is_greeting = self._is_greeting_message(message) if is_new_conversation else False

            # Log greeting detection for monitoring
            if is_new_conversation and is_greeting:
                logger.info(f"Greeting detected for new conversation from {phone_number}: '{message}'")

            booking_context['conversation_history'].append({"role": "user", "content": message})

            extracted_info = self._extract_booking_info(message, booking_context['conversation_history'])
            if extracted_info:
                booking_context.update(extracted_info)

            required_fields = ['client_name', 'service_type', 'appointment_date', 'appointment_time', 'location']
            missing_fields = [
                field for field in required_fields
                if field not in booking_context or
                booking_context[field] is None or
                (isinstance(booking_context[field], str) and booking_context[field].strip() == "")
            ]

            if missing_fields:
                logger.debug(f"Missing or invalid fields: {missing_fields}")
                response = self._generate_response(
                    message,
                    {'booking_context': booking_context, 'missing_fields': missing_fields},
                    booking_context['conversation_history']
                )
                booking_context['conversation_history'].append({"role": "assistant", "content": response})
                logger.info(f"Sending response to user: {response}")
                return response

            confirmation = self._check_booking_confirmation(
                message,
                booking_context,
                booking_context['conversation_history']
            )

            if confirmation['is_confirmed']:
                response = self._process_booking(phone_number, booking_context)
                booking_context['conversation_history'].append({"role": "assistant", "content": response})
                logger.info(f"Sending response to user: {response}")
                return response
            else:
                response = confirmation['response']
                booking_context['conversation_history'].append({"role": "assistant", "content": response})
                logger.info(f"Sending response to user: {response}")
                return response

        except Exception as e:
            language = booking_context.get('language', 'en') if 'booking_context' in locals() else 'en'
            logger.error(f"Error processing message: {str(e)}")
            response = "I apologize, but I encountered an error processing your request. Please try again or contact support."
            response = translate_response(response, language)
            booking_context['conversation_history'].append({"role": "assistant", "content": response})
            logger.info(f"Sending response to user: {response}")
            return response

    def process_voice_message(self, transcribed_text: str, phone_number: str, voice_metadata: Dict = None) -> str:
        """
        Process voice message transcription through the existing booking workflow.

        This method integrates voice message processing with the existing text message
        workflow while maintaining conversation context and adding voice-specific
        metadata for enhanced user experience and debugging.

        Args:
            transcribed_text: Text transcribed from the voice message
            phone_number: User's phone number for context and response
            voice_metadata: Optional metadata about the voice processing (confidence, duration, etc.)

        Returns:
            Response message for the user (same format as process_message)
        """
        try:
            # Initialize voice metadata if not provided
            if voice_metadata is None:
                voice_metadata = {}

            logger.info(f"Processing voice message from {phone_number}: '{transcribed_text[:50]}...'")
            logger.debug(f"Voice metadata: {voice_metadata}")

            # Get or create booking context (same as text messages)
            booking_context = get_booking_context(phone_number)
            if 'conversation_history' not in booking_context:
                booking_context['conversation_history'] = []

            # Add voice message to conversation history with metadata
            voice_message_entry = {
                "role": "user",
                "content": transcribed_text,
                "message_type": "voice",
                "voice_metadata": voice_metadata
            }
            booking_context['conversation_history'].append(voice_message_entry)

            # Log voice message processing details
            confidence = voice_metadata.get('transcription_confidence', 'unknown')
            duration = voice_metadata.get('audio_duration', 'unknown')
            logger.info(f"Voice message details - Confidence: {confidence}, Duration: {duration}s")

            # Process transcribed text through existing workflow (identical to text messages)
            extracted_info = self._extract_booking_info(transcribed_text, booking_context['conversation_history'])
            if extracted_info:
                booking_context.update(extracted_info)

            # Check for missing fields (same logic as text messages)
            required_fields = ['client_name', 'service_type', 'appointment_date', 'appointment_time', 'location']
            missing_fields = [
                field for field in required_fields
                if field not in booking_context or
                booking_context[field] is None or
                (isinstance(booking_context[field], str) and booking_context[field].strip() == "")
            ]

            if missing_fields:
                logger.debug(f"Missing or invalid fields from voice message: {missing_fields}")
                response = self._generate_response(
                    transcribed_text,
                    {'booking_context': booking_context, 'missing_fields': missing_fields},
                    booking_context['conversation_history']
                )

                # Add voice-specific context to response if transcription confidence is low
                response = self._enhance_voice_response(response, voice_metadata)

                booking_context['conversation_history'].append({
                    "role": "assistant",
                    "content": response,
                    "response_to": "voice_message"
                })
                logger.info(f"Sending response to voice message user: {response}")
                return response

            # Check for booking confirmation (same logic as text messages)
            confirmation = self._check_booking_confirmation(
                transcribed_text,
                booking_context,
                booking_context['conversation_history']
            )

            if confirmation['is_confirmed']:
                response = self._process_booking(phone_number, booking_context)

                # Add voice-specific success context
                response = self._enhance_voice_response(response, voice_metadata, is_success=True)

                booking_context['conversation_history'].append({
                    "role": "assistant",
                    "content": response,
                    "response_to": "voice_message"
                })
                logger.info(f"Sending booking confirmation response to voice message user: {response}")
                return response
            else:
                response = confirmation['response']

                # Add voice-specific context to clarification response
                response = self._enhance_voice_response(response, voice_metadata)

                booking_context['conversation_history'].append({
                    "role": "assistant",
                    "content": response,
                    "response_to": "voice_message"
                })
                logger.info(f"Sending clarification response to voice message user: {response}")
                return response

        except Exception as e:
            language = booking_context.get('language', 'en') if 'booking_context' in locals() else 'en'
            logger.error(f"Error processing voice message: {str(e)}")

            # Voice-specific error handling
            response = self._get_voice_error_response(e, voice_metadata, language)

            if 'booking_context' in locals():
                booking_context['conversation_history'].append({
                    "role": "assistant",
                    "content": response,
                    "response_to": "voice_message",
                    "error": True
                })

            logger.info(f"Sending error response to voice message user: {response}")
            return response

    def _enhance_voice_response(self, response: str, voice_metadata: Dict, is_success: bool = False) -> str:
        """
        Enhance response with voice-specific context when appropriate.

        Args:
            response: Original response message
            voice_metadata: Voice processing metadata
            is_success: Whether this is a successful booking completion

        Returns:
            Enhanced response with voice-specific context if needed
        """
        try:
            confidence = voice_metadata.get('transcription_confidence', 1.0)

            # Add confidence context for low-confidence transcriptions
            if confidence < 0.7 and not is_success:
                # Detect language for appropriate message
                language = detect_language(response)

                if language == "ar":
                    confidence_note = "\n\n(إذا لم أفهم رسالتك الصوتية بشكل صحيح، يرجى إرسال رسالة نصية)"
                else:
                    confidence_note = "\n\n(If I didn't understand your voice message correctly, please send a text message)"

                response += confidence_note
                logger.debug(f"Added low confidence note to response (confidence: {confidence:.2f})")

            # Add success acknowledgment for voice messages
            elif is_success:
                language = detect_language(response)

                if language == "ar":
                    voice_success_note = " شكراً لاستخدام الرسائل الصوتية!"
                else:
                    voice_success_note = " Thank you for using voice messages!"

                response += voice_success_note
                logger.debug("Added voice success acknowledgment to response")

            return response

        except Exception as e:
            logger.warning(f"Error enhancing voice response: {e}")
            return response  # Return original response if enhancement fails

    def _get_voice_error_response(self, error: Exception, voice_metadata: Dict, language: str) -> str:
        """
        Generate appropriate error response for voice message processing failures.

        Args:
            error: The exception that occurred
            voice_metadata: Voice processing metadata for context
            language: User's language preference

        Returns:
            User-friendly error message appropriate for voice message context
        """
        try:
            # Base error messages by language
            base_messages = {
                'en': "I apologize, but I encountered an error processing your voice message. Please try again or send a text message.",
                'ar': "أعتذر، لكنني واجهت خطأ في معالجة رسالتك الصوتية. يرجى المحاولة مرة أخرى أو إرسال رسالة نصية."
            }

            base_message = base_messages.get(language, base_messages['en'])

            # Add specific context based on voice metadata
            confidence = voice_metadata.get('transcription_confidence', 1.0)

            if confidence < 0.5:
                # Low confidence suggests transcription issues
                if language == 'ar':
                    specific_message = "يبدو أن جودة الصوت منخفضة. يرجى التحدث بوضوح أكبر أو إرسال رسالة نصية."
                else:
                    specific_message = "The audio quality seems low. Please speak more clearly or send a text message."

                logger.debug(f"Using low confidence error message (confidence: {confidence:.2f})")
                return specific_message

            # Use base message for other errors
            logger.debug("Using base voice error message")
            return base_message

        except Exception as e:
            logger.error(f"Error generating voice error response: {e}")
            # Fallback to simple English message
            return "I apologize, but I encountered an error processing your voice message. Please try again or send a text message."

def should_continue(state: MessagesState) -> str:
    """Determine whether to use tools or end the conversation."""
    last_message = state["messages"][-1]
    return "tools" if last_message.tool_calls else END

def call_model(state: MessagesState) -> dict[str, BaseMessage]:
    """Process messages through the agent."""
    try:
        if not state.messages:
            response = translate_response(
                "I apologize, but I didn't receive any message to process.", 'en'
            )
            logger.info(f"Sending response to user: {response}")
            return {"messages": [{"type": "assistant", "content": response}]}

        last_message = state.messages[-1]

        if hasattr(last_message, 'type'):
            message_type = last_message.type
            message_content = last_message.content
            user_id = getattr(last_message, 'user_id', 'default_user')
        else:
            message_type = last_message.get("type")
            message_content = last_message.get("content", "")
            user_id = last_message.get("user_id", "default_user")

        if message_type != "user":
            response = translate_response(
                "I apologize, but I need a user message to process.", 'en'
            )
            logger.info(f"Sending response to user: {response}")
            return {"messages": [{"type": "assistant", "content": response}]}

        agent = BookingAgent()
        response = agent.process_message(message_content, user_id)
        logger.info(f"Sending response to user: {response}")
        return {"messages": [{"type": "assistant", "content": response}]}

    except Exception as e:
        logger.error(f"Error in call_model: {str(e)}")
        response = translate_response(
            "I apologize, but I encountered an error processing your request.", 'en'
        )
        logger.info(f"Sending response to user: {response}")
        return {"messages": [{"type": "assistant", "content": response}]}

workflow = StateGraph(MessagesState)
workflow.add_node("agent", call_model)
workflow.add_node("tools", ToolNode(tools))
workflow.set_entry_point("agent")
workflow.add_conditional_edges("agent", should_continue)
workflow.add_edge("tools", "agent")
agent = workflow.compile()